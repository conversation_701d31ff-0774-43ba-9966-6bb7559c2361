#include "drv_soft_i2c.h"

#define i2c_delay_us 1

struct rt_mutex i2c_lock;

rt_inline rt_err_t rt_i2c_bus_lock(rt_tick_t timeout)
{
    return rt_mutex_take(&i2c_lock, timeout);
}

rt_inline rt_err_t rt_i2c_bus_unlock(void)
{
    return rt_mutex_release(&i2c_lock);
}

rt_inline void i2c_delay(void)
{
    rt_hw_us_delay((i2c_delay_us + 1) >> 1);
}

rt_inline void i2c_delay2(void)
{
    rt_hw_us_delay(i2c_delay_us);
}

/**
 * release scl line, and wait scl line to high.
 */
static rt_err_t SCL_H(void)
{
    rt_tick_t start;

    Pin_SCL_H;

    if (!Read_SCL_Pin)
        goto done;

    start = rt_tick_get();
    while (!Read_SCL_Pin)
    {
        if ((rt_tick_get() - start) > RT_TICK_PER_SECOND)
            return -RT_ETIMEOUT;
        rt_thread_delay((RT_TICK_PER_SECOND + 1) >> 1);
    }

done:
    i2c_delay();

    return RT_EOK;
}

// 发送IIC起始信号
static void i2c_start(void)
{
    Pin_SDA_L;
    i2c_delay();
    Pin_SCL_L;
}

//重启总线
static void i2c_restart(void)
{
    Pin_SDA_H;
    SCL_H();
    i2c_delay();
    Pin_SDA_L;
    i2c_delay();
    Pin_SCL_L;
}

// 发送IIC停止信号
static void i2c_stop(void)
{
    Pin_SDA_L;
    i2c_delay();
    SCL_H();
    i2c_delay();
    Pin_SDA_H;
    i2c_delay2();
}

// IIC等待ACK信号
rt_inline rt_bool_t i2c_waitack(void)
{
    rt_bool_t ack = 0;

    Pin_SDA_H;
    i2c_delay();

    if (SCL_H() < 0)
    {
        return -RT_ETIMEOUT;
    }

    ack = !Read_SDA_Pin; /* ACK : SDA pin is pulled low */

    Pin_SCL_L;

    return ack;
}

//是否发送ack
static rt_err_t i2c_send_ack_or_nack(int ack)
{
    if (ack)
    {
        Pin_SDA_L;
    }
    i2c_delay();

    if (SCL_H() < 0)
    {
        return -RT_ETIMEOUT;
    }

    Pin_SCL_L;

    return RT_EOK;
}

// IIC发送一个字节
static rt_int32_t i2c_writeb(rt_uint8_t data)
{
    rt_int32_t i;
    rt_uint8_t bit;

    for (i = 7; i >= 0; i--)
    {
        Pin_SCL_L;

        bit = (data >> i) & 0x01;
        bit ? Pin_SDA_H : Pin_SDA_L;
        i2c_delay();

        if (SCL_H() < 0)
        {
            return -RT_ETIMEOUT;
        }
    }
    Pin_SCL_L;
    i2c_delay();

    return i2c_waitack();
}

// IIC读取一个字节
static rt_int32_t i2c_readb(void)
{
    rt_uint8_t i;
    rt_uint8_t data = 0;

    Pin_SDA_H;
    i2c_delay();

    for (i = 0; i < 8; i++)
    {
        data <<= 1;

        if (SCL_H() < 0)
        {
            return -RT_ETIMEOUT;
        }

        if (Read_SDA_Pin)
        {
            data |= 0x01;
        }

        Pin_SCL_L;
        i2c_delay();
    }

    return data;
}

static rt_size_t i2c_send_bytes(struct rt_i2c_msg *msg)
{
    rt_int32_t ret;
    rt_size_t bytes = 0;
    const uint8_t *ptr = msg->buf;
    rt_int32_t count = msg->len;
    uint16_t ignore_nack = msg->flags & RT_I2C_IGNORE_NACK;

    while (count > 0)
    {
        ret = i2c_writeb(*ptr);

        if ((ret > 0) || (ignore_nack && (ret == 0)))
        {
            count--;
            ptr++;
            bytes++;
        }
        else if (ret == 0)
        {
            return 0;
        }
        else
        {
            return ret;
        }
    }

    return bytes;
}

static rt_size_t i2c_recv_bytes(struct rt_i2c_msg *msg)
{
    rt_int32_t val;
    rt_int32_t bytes = 0; /* actual bytes */
    uint8_t *ptr = msg->buf;
    rt_int32_t count = msg->len;
    const uint32_t flags = msg->flags;

    while (count > 0)
    {
        val = i2c_readb();
        if (val >= 0)
        {
            *ptr = val;
            bytes++;
        }
        else
        {
            break;
        }

        ptr++;
        count--;

        if (!(flags & RT_I2C_NO_READ_ACK))
        {
            val = i2c_send_ack_or_nack(count);
            if (val < 0)
                return val;
        }
    }

    return bytes;
}

static rt_int32_t i2c_send_address(uint8_t addr, rt_int32_t retries)
{
    rt_int32_t i;
    rt_err_t ret = 0;

    for (i = 0; i <= retries; i++)
    {
        ret = i2c_writeb(addr);
        if (ret == 1 || i == retries)
            break;
        i2c_stop();
        i2c_delay2();
        i2c_start();
    }

    return ret;
}

static rt_err_t i2c_bit_send_address(struct rt_i2c_msg *msg)
{
    rt_uint16_t flags = msg->flags;
    rt_uint16_t ignore_nack = msg->flags & RT_I2C_IGNORE_NACK;

    rt_uint8_t addr1, addr2;
    rt_int32_t retries;
    rt_err_t ret;

    retries = ignore_nack ? 0 : 0;

    if (flags & RT_I2C_ADDR_10BIT)
    {
        addr1 = 0xf0 | ((msg->addr >> 7) & 0x06);
        addr2 = msg->addr & 0xff;

        ret = i2c_send_address(addr1, retries);
        if ((ret != 1) && !ignore_nack)
        {
            return -RT_EIO;
        }

        ret = i2c_writeb(addr2);
        if ((ret != 1) && !ignore_nack)
        {
            return -RT_EIO;
        }
        if (flags & RT_I2C_RD)
        {
            i2c_restart();
            addr1 |= 0x01;
            ret = i2c_send_address(addr1, retries);
            if ((ret != 1) && !ignore_nack)
            {
                return -RT_EIO;
            }
        }
    }
    else
    {
        /* 7-bit addr */
        addr1 = msg->addr << 1;
        if (flags & RT_I2C_RD)
        {
            addr1 |= 0x01;
        }
        ret = i2c_send_address(addr1, retries);
        if ((ret != 1) && !ignore_nack)
            return -RT_EIO;
    }

    return RT_EOK;
}

static rt_size_t i2c_bit_xfer(struct rt_i2c_msg msgs[], uint16_t num)
{
    struct rt_i2c_msg *msg = RT_NULL;
    rt_int32_t i, ret;
    uint16_t ignore_nack;

    for (i = 0; i < num; i++)
    {
        msg = &msgs[i];
        ignore_nack = msg->flags & RT_I2C_IGNORE_NACK;
        if (!(msg->flags & RT_I2C_NO_START))
        {
            if (i)
            {
                i2c_restart();
            }
            else
            {
                i2c_start();
            }
            ret = i2c_bit_send_address(msg);
            if ((ret != RT_EOK) && !ignore_nack)
            {
                goto out;
            }
        }
        if (msg->flags & RT_I2C_RD)
        {
            ret = i2c_recv_bytes(msg);
            if (ret < msg->len)
            {
                if (ret >= 0)
                    ret = -RT_EIO;
                goto out;
            }
        }
        else
        {
            ret = i2c_send_bytes(msg);
            if (ret < msg->len)
            {
                if (ret >= 0)
                    ret = -RT_ERROR;
                goto out;
            }
        }
    }
    ret = i;

out:
    if (!(msg->flags & RT_I2C_NO_STOP))
    {
        i2c_stop();
    }

    return ret;
}

rt_size_t rt_i2c_transfer(struct rt_i2c_msg msgs[],
                          rt_uint32_t num)
{
    rt_err_t ret;

    rt_mutex_take(&i2c_lock, RT_WAITING_FOREVER);
    ret = i2c_bit_xfer(msgs, num);
    rt_mutex_release(&i2c_lock);

    return ret;
}

rt_size_t rt_i2c_master_send(rt_uint16_t addr,
                             rt_uint16_t flags,
                             const rt_uint8_t *buf,
                             rt_uint32_t count)
{
    rt_err_t ret;
    struct rt_i2c_msg msg;

    msg.addr = addr;
    msg.flags = flags;
    msg.len = count;
    msg.buf = (rt_uint8_t *)buf;

    ret = rt_i2c_transfer(&msg, 1);

    return (ret > 0) ? count : ret;
}

rt_size_t rt_i2c_master_recv(rt_uint16_t addr,
                             rt_uint16_t flags,
                             rt_uint8_t *buf,
                             rt_uint32_t count)
{
    rt_err_t ret;
    struct rt_i2c_msg msg;
    RT_ASSERT(buf != RT_NULL);

    msg.addr = addr;
    msg.flags = flags | RT_I2C_RD;
    msg.len = count;
    msg.buf = buf;

    ret = rt_i2c_transfer(&msg, 1);

    return (ret > 0) ? count : ret;
}

/**
 * if i2c is locked, this function will unlock it
 *
 * @param stm32 config class
 *
 * @return RT_EOK indicates successful unlock.
 */
static rt_err_t stm32_i2c_bus_unlock(void)
{
    rt_int32_t i = 0;

    if (!Read_SDA_Pin)
    {
        while (i++ < 9)
        {
            Pin_SCL_H;
            rt_hw_us_delay(100);
            Pin_SCL_L;
            rt_hw_us_delay(100);
        }
    }
    if (!Read_SDA_Pin)
    {
        return -RT_ERROR;
    }

    return RT_EOK;
}

int rt_hw_i2c_init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    __HAL_RCC_GPIOB_CLK_ENABLE(); // 打开GPIOB口时钟

    //输出口配置
    GPIO_InitStruct.Pin = GPIO_PIN_8 | GPIO_PIN_9;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_8 | GPIO_PIN_9, GPIO_PIN_SET);

    rt_mutex_init(&i2c_lock, "i2c_bus_lock", RT_IPC_FLAG_FIFO);

//    stm32_i2c_bus_unlock();

    return RT_EOK;
}
INIT_BOARD_EXPORT(rt_hw_i2c_init);
