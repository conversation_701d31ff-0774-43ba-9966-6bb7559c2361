/*
 * Copyright (c) 2006-2019, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2019-11-09     xiangxistu   first version
 * 2020-05-18     chenyaxing   modify stm32_uart_config struct
 */

#include "stdlib.h"
#include "drv_common.h"
#include "drv_uart.h"

#include "app_smartcard.h"
#include "app_gprs.h"

UART_HandleTypeDef huart1;
UART_HandleTypeDef huart2;
DMA_HandleTypeDef hdma_usart1_rx;
DMA_HandleTypeDef hdma_usart1_tx;
DMA_HandleTypeDef hdma_usart2_rx;
DMA_HandleTypeDef hdma_usart2_tx;

typedef struct
{
    uint16_t lenth;
    uint8_t *buf;
} uart_fifo_t;
//这里是发送和接收缓冲区的定义
ALIGN(RT_ALIGN_SIZE)
uint8_t uart1_rx_buf[USART1_RX_BUF_SIZE] = {0};
ALIGN(RT_ALIGN_SIZE)
uint8_t uart2_rx_buf[USART2_RX_BUF_SIZE] = {0};

//static __IO uart_fifo_t fifo_uart1_tx = {0, 0, 0, uart1_tx_dma_buf, 0}; //串口1的环形缓冲区
static __IO uart_fifo_t fifo_uart1_rx = {0, uart1_rx_buf}; //串口1的环形缓冲区

//static __IO uart_fifo_t fifo_uart2_tx = {0, 0, 0, uart2_tx_dma_buf, 0}; //串口2的环形缓冲区
static __IO uart_fifo_t fifo_uart2_rx = {0, uart2_rx_buf}; //串口2的环形缓冲区

/**
  * Enable DMA controller clock
  */
void MX_DMA_Init(void)
{
    /* DMA controller clock enable */
    __HAL_RCC_DMA1_CLK_ENABLE();

    /* DMA interrupt init */
    /* DMA1_Channel2_3_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(DMA1_Channel2_3_IRQn, 1, 0);
    HAL_NVIC_EnableIRQ(DMA1_Channel2_3_IRQn);
    /* DMA1_Channel4_5_IRQn interrupt configuration */
    HAL_NVIC_SetPriority(DMA1_Channel4_5_IRQn, 1, 0);
    HAL_NVIC_EnableIRQ(DMA1_Channel4_5_IRQn);
}

/* USART1 init function */

static void MX_USART1_UART_Init(void)
{
    huart1.Instance = USART1;
    huart1.Init.BaudRate = 9600;
    huart1.Init.WordLength = UART_WORDLENGTH_8B;
    huart1.Init.StopBits = UART_STOPBITS_1;
    huart1.Init.Parity = UART_PARITY_NONE;
    huart1.Init.Mode = UART_MODE_TX_RX;
    huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart1.Init.OverSampling = UART_OVERSAMPLING_16;
    huart1.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
    if (HAL_UART_Init(&huart1) != HAL_OK)
    {
        Error_Handler();
    }
}
/* USART2 init function */

static void MX_USART2_UART_Init(void)
{
    huart2.Instance = USART2;
    huart2.Init.BaudRate = 9600;
    huart2.Init.WordLength = UART_WORDLENGTH_8B;
    huart2.Init.StopBits = UART_STOPBITS_1;
    huart2.Init.Parity = UART_PARITY_NONE;
    huart2.Init.Mode = UART_MODE_TX_RX;
    huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    huart2.Init.OverSampling = UART_OVERSAMPLING_16;
    huart2.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
    huart2.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
    if (HAL_UART_Init(&huart2) != HAL_OK)
    {
        Error_Handler();
    }
}

void HAL_UART_MspInit(UART_HandleTypeDef *uartHandle)
{

    GPIO_InitTypeDef GPIO_InitStruct = {0};

    if (uartHandle->Instance == USART1)
    {
        /* USART1 clock enable */
        __HAL_RCC_USART1_CLK_ENABLE();

        __HAL_RCC_GPIOA_CLK_ENABLE();

        /**USART1 GPIO Configuration
        PA9     ------> USART1_TX
        PA10     ------> USART1_RX
        */
        GPIO_InitStruct.Pin = GPIO_PIN_9 | GPIO_PIN_10;
        GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
        GPIO_InitStruct.Pull = GPIO_PULLUP;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
        GPIO_InitStruct.Alternate = GPIO_AF1_USART1;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

#ifndef RT_USING_FINSH
        /* USART1 DMA Init */
        /* USART1_RX Init */
        hdma_usart1_rx.Instance = DMA1_Channel3;
        hdma_usart1_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
        hdma_usart1_rx.Init.PeriphInc = DMA_PINC_DISABLE;
        hdma_usart1_rx.Init.MemInc = DMA_MINC_ENABLE;
        hdma_usart1_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
        hdma_usart1_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
        hdma_usart1_rx.Init.Mode = DMA_NORMAL;
        hdma_usart1_rx.Init.Priority = DMA_PRIORITY_HIGH;
        if (HAL_DMA_Init(&hdma_usart1_rx) != HAL_OK)
        {
            Error_Handler();
        }

        __HAL_LINKDMA(uartHandle, hdmarx, hdma_usart1_rx);

        /* USART1_TX Init */
        //        hdma_usart1_tx.Instance                 = DMA1_Channel2;
        //        hdma_usart1_tx.Init.Direction           = DMA_MEMORY_TO_PERIPH;
        //        hdma_usart1_tx.Init.PeriphInc           = DMA_PINC_DISABLE;
        //        hdma_usart1_tx.Init.MemInc              = DMA_MINC_ENABLE;
        //        hdma_usart1_tx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
        //        hdma_usart1_tx.Init.MemDataAlignment    = DMA_MDATAALIGN_BYTE;
        //        hdma_usart1_tx.Init.Mode                = DMA_NORMAL;
        //        hdma_usart1_tx.Init.Priority            = DMA_PRIORITY_MEDIUM;
        //        if (HAL_DMA_Init(&hdma_usart1_tx) != HAL_OK)
        //        {
        //            Error_Handler();
        //        }

        //        __HAL_LINKDMA(uartHandle,hdmatx,hdma_usart1_tx);

        /* USART1 interrupt Init */
        HAL_NVIC_SetPriority(USART1_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(USART1_IRQn);
#endif
    }
    else if (uartHandle->Instance == USART2)
    {
        /* USART2 clock enable */
        __HAL_RCC_USART2_CLK_ENABLE();

        __HAL_RCC_GPIOA_CLK_ENABLE();
        /**USART2 GPIO Configuration
        PA2     ------> USART2_TX
        PA3     ------> USART2_RX
        */
        GPIO_InitStruct.Pin = GPIO_PIN_2 | GPIO_PIN_3;
        GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
        GPIO_InitStruct.Pull = GPIO_PULLUP;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
        GPIO_InitStruct.Alternate = GPIO_AF1_USART2;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

        /* USART2 DMA Init */
        /* USART2_RX Init */
        hdma_usart2_rx.Instance = DMA1_Channel5;
        hdma_usart2_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
        hdma_usart2_rx.Init.PeriphInc = DMA_PINC_DISABLE;
        hdma_usart2_rx.Init.MemInc = DMA_MINC_ENABLE;
        hdma_usart2_rx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
        hdma_usart2_rx.Init.MemDataAlignment = DMA_MDATAALIGN_BYTE;
        hdma_usart2_rx.Init.Mode = DMA_NORMAL;
        hdma_usart2_rx.Init.Priority = DMA_PRIORITY_MEDIUM;
        if (HAL_DMA_Init(&hdma_usart2_rx) != HAL_OK)
        {
            Error_Handler();
        }

        __HAL_LINKDMA(uartHandle, hdmarx, hdma_usart2_rx);

        /* USART2_TX Init */
        //        hdma_usart2_tx.Instance                 = DMA1_Channel4;
        //        hdma_usart2_tx.Init.Direction           = DMA_MEMORY_TO_PERIPH;
        //        hdma_usart2_tx.Init.PeriphInc           = DMA_PINC_DISABLE;
        //        hdma_usart2_tx.Init.MemInc              = DMA_MINC_ENABLE;
        //        hdma_usart2_tx.Init.PeriphDataAlignment = DMA_PDATAALIGN_BYTE;
        //        hdma_usart2_tx.Init.MemDataAlignment    = DMA_MDATAALIGN_BYTE;
        //        hdma_usart2_tx.Init.Mode                = DMA_NORMAL;
        //        hdma_usart2_tx.Init.Priority            = DMA_PRIORITY_MEDIUM;
        //        if (HAL_DMA_Init(&hdma_usart2_tx) != HAL_OK)
        //        {
        //            Error_Handler();
        //        }

        //        __HAL_LINKDMA(uartHandle,hdmatx,hdma_usart2_tx);

        /* USART2 interrupt Init */
        HAL_NVIC_SetPriority(USART2_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(USART2_IRQn);
    }
}

void HAL_UART_MspDeInit(UART_HandleTypeDef *uartHandle)
{

    if (uartHandle->Instance == USART1)
    {
        /* Peripheral clock disable */
        __HAL_RCC_USART1_CLK_DISABLE();

        /**USART1 GPIO Configuration
        PB6     ------> USART1_TX
        PB7     ------> USART1_RX
        */
        HAL_GPIO_DeInit(GPIOA, GPIO_PIN_9 | GPIO_PIN_10);

        /* USART1 DMA DeInit */
        HAL_DMA_DeInit(uartHandle->hdmarx);
        //        HAL_DMA_DeInit(uartHandle->hdmatx);

        /* USART1 interrupt Deinit */
        HAL_NVIC_DisableIRQ(USART1_IRQn);
    }
    else if (uartHandle->Instance == USART2)
    {
        /* Peripheral clock disable */
        __HAL_RCC_USART2_CLK_DISABLE();

        /**USART2 GPIO Configuration
        PA2     ------> USART2_TX
        PA3     ------> USART2_RX
        */
        HAL_GPIO_DeInit(GPIOA, GPIO_PIN_2 | GPIO_PIN_3);

        /* USART2 DMA DeInit */
        HAL_DMA_DeInit(uartHandle->hdmarx);
        //        HAL_DMA_DeInit(uartHandle->hdmatx);

        /* USART2 interrupt Deinit */
        HAL_NVIC_DisableIRQ(USART2_IRQn);
    }
}

#ifdef RT_USING_FINSH
char rt_hw_console_getchar(void)
{
    int ch = -1;

    if (__HAL_UART_GET_FLAG(&huart1, UART_FLAG_RXNE) != RESET)
    {
#if defined(SOC_SERIES_STM32L4) || defined(SOC_SERIES_STM32F7) || defined(SOC_SERIES_STM32F0) || defined(SOC_SERIES_STM32L0) || defined(SOC_SERIES_STM32G0) || defined(SOC_SERIES_STM32H7) || defined(SOC_SERIES_STM32G4)
        ch = huart1.Instance->RDR & 0xff;
#else
        ch = handle.Instance->DR & 0xff;
#endif
    }
    else
    {
        if (__HAL_UART_GET_FLAG(&huart1, UART_FLAG_ORE) != RESET)
        {
            __HAL_UART_CLEAR_OREFLAG(&huart1);
        }
        rt_thread_mdelay(10);
    }

    return ch;
}
#endif /* RT_USING_FINSH */

//空闲中断处理函数
void USER_UART_IRQHandler(UART_HandleTypeDef *huart)
{
    uint32_t temp = 0;

    if (USART1 == huart->Instance) //判断是否是串口1
    {
        if (RESET != __HAL_UART_GET_FLAG(&huart1, UART_FLAG_IDLE)) //判断是否是空闲中断
        {
            __HAL_UART_CLEAR_IDLEFLAG(&huart1); //清楚空闲中断标志（否则会一直不断进入中断）
            temp = huart->Instance->ISR;        //软件序列清除IDLE位
            temp = huart->Instance->RDR;        //先读USART_SR,然后读USART_DR

            fifo_uart1_rx.lenth = USART1_RX_BUF_SIZE - __HAL_DMA_GET_COUNTER(&hdma_usart1_rx); //计算接收到的数据长度

            //            HAL_UART_DMAStop(&huart1);
            HAL_UART_AbortReceive(&huart1);

            if (fifo_uart1_rx.lenth == 0)
            {
                hdma_usart1_rx.Instance->CNDTR = USART1_RX_BUF_SIZE;
                HAL_UART_Receive_DMA(&huart1, (uint8_t *)fifo_uart1_rx.buf, USART1_RX_BUF_SIZE); //开启DMA接收
            }
            else
            {
                //向微信模块处理线程发送信号
                rt_sem_release(gprs_recv_sem);
            }
        }
    }
    else if (USART2 == huart->Instance)
    {
        if (RESET != __HAL_UART_GET_FLAG(&huart2, UART_FLAG_IDLE)) //判断是否是空闲中断
        {
            __HAL_UART_CLEAR_IDLEFLAG(&huart2); //清楚空闲中断标志（否则会一直不断进入中断）
            temp = huart->Instance->ISR;        //软件序列清除IDLE位
            temp = huart->Instance->RDR;        //先读USART_SR,然后读USART_DR

            fifo_uart2_rx.lenth = USART2_RX_BUF_SIZE - __HAL_DMA_GET_COUNTER(&hdma_usart2_rx); //计算接收到的数据长度

            HAL_UART_AbortReceive(&huart2);
            //            HAL_UART_DMAStop(&huart2);

            if (fifo_uart2_rx.lenth == 0)
            {
                hdma_usart2_rx.Instance->CNDTR = USART2_RX_BUF_SIZE;
                HAL_UART_Receive_DMA(&huart2, (uint8_t *)fifo_uart2_rx.buf, USART2_RX_BUF_SIZE); //开启DMA接收
            }
            else
            {
                //向刷卡处理线程发送信号
                rt_sem_release(card_recv_sem);
            }
        }
    }
}

/**********************************************************************************
 * 函数名：chk_xrl
 * 描述  ：异或校验
 * 输入  ：*data 串口数据
 *         length 数据长度
 * 输出  ：无
 * 返回  ：校验和
 * 调用  ：外部调用 
 * 举例  ：
 **********************************************************************************/
uint8_t chk_xrl(uint8_t *data, uint16_t length)
{
    uint8_t i = 0;
    uint8_t retval = 0;

    while (length)
    {
        retval ^= *data++;
        length--;
    }

    return retval;
}

//参数1：串口接收环形缓冲区对象指针
//发送函数:调用uart1_dma_send()函数将数据放入环形缓冲区并发送，此函数在上面实现
rt_err_t uart_send_data(UART_HandleTypeDef *huart, uint8_t *buf, uint16_t len)
{
    rt_mutex_t usart_send_lock;

    usart_send_lock = rt_mutex_create("mutex_usart_send", RT_IPC_FLAG_FIFO);
    if (usart_send_lock == RT_NULL)
    {
        return RT_ERROR;
    }

    rt_mutex_take(usart_send_lock, RT_WAITING_FOREVER);
    while (HAL_OK != HAL_UART_Transmit(huart, buf, len, 500))
        ;
    rt_mutex_release(usart_send_lock);

    rt_mutex_delete(usart_send_lock);

    return RT_EOK;
}

void rt_hw_console_output(const char *str)
{
    rt_size_t i = 0, size = 0;
    char a = '\r';

    size = rt_strlen(str);
    for (i = 0; i < size; i++)
    {
        if (*(str + i) == '\n')
        {
            HAL_UART_Transmit(&huart1, (uint8_t *)&a, 1, 1);
        }
        HAL_UART_Transmit(&huart1, (uint8_t *)(str + i), 1, 1);
    }
}

rt_err_t get_recevie_buf(UART_HandleTypeDef *huart, uint8_t *ptr)
{
    uint8_t i = 0, temp = 0;

    if (huart->Instance == USART1)
    {
        rt_memcpy(ptr, fifo_uart1_rx.buf, fifo_uart1_rx.lenth);
        rt_memset(fifo_uart1_rx.buf, 0, USART1_RX_BUF_SIZE);
        hdma_usart1_rx.Instance->CNDTR = USART1_RX_BUF_SIZE;
        HAL_UART_Receive_DMA(&huart1, (uint8_t *)fifo_uart1_rx.buf, USART1_RX_BUF_SIZE); //开启DMA接收
    }
    else if (huart->Instance == USART2)
    {
        rt_memcpy(ptr, fifo_uart2_rx.buf, fifo_uart2_rx.lenth);
        rt_memset(fifo_uart2_rx.buf, 0, USART2_RX_BUF_SIZE);
        hdma_usart2_rx.Instance->CNDTR = USART2_RX_BUF_SIZE;
        HAL_UART_Receive_DMA(&huart2, (uint8_t *)fifo_uart2_rx.buf, USART2_RX_BUF_SIZE); //开启DMA接收
    }

    return RT_EOK;
}

int rt_hw_usart_init()
{
    rt_base_t level;

    MX_DMA_Init();
    HAL_UART_Receive_DMA(&huart1, (uint8_t *)uart1_rx_buf, USART1_RX_BUF_SIZE); //开启DMA接收
    HAL_UART_Receive_DMA(&huart2, (uint8_t *)uart2_rx_buf, USART2_RX_BUF_SIZE); //开启DMA接收

    MX_USART1_UART_Init();
    MX_USART2_UART_Init();

    return RT_EOK;
}
//INIT_BOARD_EXPORT(rt_hw_usart_init);

void enable_usart_irq(void)
{
    __HAL_UART_ENABLE_IT(&huart1, UART_IT_IDLE); //开启空闲中断
    __HAL_UART_ENABLE_IT(&huart2, UART_IT_IDLE); //开启空闲中断
}
//INIT_ENV_EXPORT(enable_dma_irq);
