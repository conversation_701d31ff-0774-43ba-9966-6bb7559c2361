/*
 * Copyright (c) 2006-2018, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2018-10-30     SummerGift   first version
 * 2019-01-03     zylx         modify dma support
 * 2020-06-03     chenyaxing   modify uart_config struct
 */

#ifndef __UART_CONFIG_H__
#define __UART_CONFIG_H__

#include <rtthread.h>
#include <board.h>

#include "stdbool.h"

#ifdef __cplusplus
extern "C" {
#endif


#define USART1_RX_BUF_SIZE  50

#define USART2_RX_BUF_SIZE  10

extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart2;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern DMA_HandleTypeDef hdma_usart1_tx;
extern DMA_HandleTypeDef hdma_usart2_rx;
extern DMA_HandleTypeDef hdma_usart2_tx;

extern void enable_usart_irq(void);

extern rt_err_t get_recevie_buf(UART_HandleTypeDef *huart, uint8_t *ptr);
extern rt_err_t uart_send_data(UART_HandleTypeDef *huart, uint8_t *buf, uint16_t len);
extern uint8_t chk_xrl(uint8_t *data, uint16_t length);

extern void USER_UART_IRQHandler(UART_HandleTypeDef *huart);

#ifdef __cplusplus
}
#endif

#endif


