#include "drv_gpio.h"
#include "app_process.h"

int rt_hw_pin_init(void)
{
  GPIO_InitTypeDef handler;

  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOC_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();
  __HAL_RCC_GPIOF_CLK_ENABLE();

  //初始化输出端口
  /*Configure GPIO pins : PA0 PA1 PA4 PA5
                           PA6 PA7 */
  handler.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_4 | GPIO_PIN_5 | GPIO_PIN_6 | GPIO_PIN_7;
  handler.Mode = GPIO_MODE_OUTPUT_PP;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOA, &handler);

  /*Configure GPIO pins : PB0 PB1 PB2 */
  handler.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2;
  handler.Mode = GPIO_MODE_OUTPUT_PP;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOB, &handler);

  /*Configure GPIO pins : PC13 PC0 PC1 PC2
                           PC3 PC4 PC5 */
  handler.Pin = GPIO_PIN_13 | GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_4 | GPIO_PIN_5;
  handler.Mode = GPIO_MODE_OUTPUT_PP;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOC, &handler);

  /*Configure GPIO pin : PC15 */
  handler.Pin = GPIO_PIN_15;
  handler.Mode = GPIO_MODE_OUTPUT_PP;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOC, &handler);

  /*Configure GPIO pins : PF4 PF5 */
  handler.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_4 | GPIO_PIN_5;
  handler.Mode = GPIO_MODE_OUTPUT_PP;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOF, &handler);

  //初始化输入端口
  /*Configure GPIO pins : PA11 PA12 PA15 */
  handler.Pin = GPIO_PIN_11 | GPIO_PIN_12 | GPIO_PIN_15;
  handler.Mode = GPIO_MODE_INPUT;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOA, &handler);

  /*Configure GPIO pins : PB3 PB4 PB5 PB6
                           PB7 */
  handler.Pin = GPIO_PIN_3 | GPIO_PIN_4 | GPIO_PIN_5 | GPIO_PIN_6 | GPIO_PIN_7;
  handler.Mode = GPIO_MODE_INPUT;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOB, &handler);

  /*Configure GPIO pins : PC10 PC11 PC12 */
  handler.Pin = GPIO_PIN_2 | GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12;
  handler.Mode = GPIO_MODE_INPUT;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOC, &handler);

  /*Configure GPIO pin : PD2 */
  handler.Pin = GPIO_PIN_2;
  handler.Mode = GPIO_MODE_INPUT;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOD, &handler);

  /*Configure GPIO pins : PF6 PF7 */
  handler.Pin = GPIO_PIN_6 | GPIO_PIN_7;
  handler.Mode = GPIO_MODE_INPUT;
  handler.Pull = GPIO_PULLUP;
  handler.Speed = GPIO_SPEED_FREQ_HIGH;
  HAL_GPIO_Init(GPIOF, &handler);

  //中断脚初始化
  /*Configure GPIO pins : PB10 PB11 PB12 PB13
                           PB14 PB15 */
  handler.Pin = GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12 | GPIO_PIN_13 | GPIO_PIN_14 | GPIO_PIN_15;
  handler.Mode = GPIO_MODE_IT_FALLING;
  handler.Pull = GPIO_PULLUP;
  HAL_GPIO_Init(GPIOB, &handler);

  /*Configure GPIO pins : PC6 PC7 PC8 PC9 */
  handler.Pin = GPIO_PIN_6 | GPIO_PIN_7 | GPIO_PIN_8 | GPIO_PIN_9;
  handler.Mode = GPIO_MODE_IT_FALLING;
  handler.Pull = GPIO_PULLUP;
  HAL_GPIO_Init(GPIOC, &handler);

  /* EXTI interrupt init*/
  HAL_NVIC_SetPriority(EXTI4_15_IRQn, 3, 1);
  HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);

  //输出引脚默认低电平
  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_4 | GPIO_PIN_5 | GPIO_PIN_6 | GPIO_PIN_7, GPIO_PIN_RESET);

  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2, GPIO_PIN_RESET);

  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13 | GPIO_PIN_15 | GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_4 | GPIO_PIN_5, GPIO_PIN_RESET);

  HAL_GPIO_WritePin(GPIOF, GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_4 | GPIO_PIN_5, GPIO_PIN_RESET);

  return RT_EOK;
}
//INIT_BOARD_EXPORT(hw_gpio_init);
