#include "app_keypoll.h"

#define THREAD_PRIORITY 3
#define THREAD_TIMESLICE 1

/* 消息队列控制块 */
struct rt_mailbox key_mb;
/* 消息队列中用到的放置消息的内存池 */
static uint8_t mb_pool[4];

ALIGN(RT_ALIGN_SIZE)
static char keyPoll_stack[200];
static struct rt_thread keyPoll;

static void keyPoll_entry(void *arg)
{
    rt_err_t result;
    uint8_t cmd;

    while (1)
    {
        cmd = Key_Scan();
        if (cmd != RT_NULL && (cmd & KEY_UP) == KEY_UP)
        {
            cmd &= ~KEY_UP;
            /* 发送消息到消息队列中 */
            rt_mb_send(&key_mb, (rt_uint32_t)cmd);
        }
        rt_thread_delay(20);
    }
}

int keyPoll_thread(void)
{
    rt_err_t result;

    /* 初始化一个 mailbox */
    result = rt_mb_init(&key_mb,
                        "key_mb",            /* 名称是 mbt */
                        &mb_pool[0],         /* 邮箱用到的内存池是 mb_pool */
                        sizeof(mb_pool) / 4, /* 邮箱中的邮件数目，因为一封邮件占 4 字节 */
                        RT_IPC_FLAG_FIFO);   /* 采用 FIFO 方式进行线程等待 */
    if (result != RT_EOK)
    {
        rt_kprintf("init mailbox failed.\n");
        return RT_ERROR;
    }

    rt_thread_init(&keyPoll,
                   "keyPoll",
                   keyPoll_entry,
                   RT_NULL,
                   &keyPoll_stack[0],
                   sizeof(keyPoll_stack),
                   THREAD_PRIORITY,
                   THREAD_TIMESLICE);
    rt_thread_startup(&keyPoll);

    return RT_EOK;
}
INIT_APP_EXPORT(keyPoll_thread);
