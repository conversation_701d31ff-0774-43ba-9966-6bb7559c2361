#ifndef __DRV_I2C_H__
#define __DRV_I2C_H__


#include "stm32f0xx.h"
#include <stdbool.h>
 
 
#define Pin_SCL_L		(GPIOB->BRR = (uint32_t)GPIO_PIN_8)
#define Pin_SCL_H		(GPIOB->BSRR = (uint32_t)GPIO_PIN_8)
 
#define Pin_SDA_L		(GPIOB->BRR = (uint32_t)GPIO_PIN_9)
#define Pin_SDA_H		(GPIOB->BSRR = (uint32_t)GPIO_PIN_9)
 
#define Read_SDA_Pin	(GPIOB->IDR & GPIO_PIN_9)

//#define I2C_SDA_OUT()     {GPIOB->MODER &= ~(3<<(9*2)); GPIOB->MODER |= 0<<(9*2);}
//#define I2C_SDA_IN()      {GPIOB->MODER &= ~(3<<(9*2)); GPIOB->MODER |= 1<<(9*2);}

bool EE_Write_Data(uint16_t addr, uint8_t data);
uint8_t EE_Read_Data(uint16_t addr);
bool EE_Page_Write(uint16_t addr, uint8_t *buf, uint16_t len);
bool EE_Page_Read(uint16_t addr, uint8_t *buf, uint16_t len);

bool EE_Write_NByte(uint16_t WriteAddr, uint8_t* pBuffer, uint16_t NumByteToWrite);
bool EE_Read_NByte(uint16_t ReadAddr, uint8_t* pBuffer, uint16_t NumByteToRead);

#endif
