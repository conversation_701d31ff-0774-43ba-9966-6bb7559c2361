#ifndef __APP_SMARTCARD_H__

#include "stm32f0xx.h"
#include "rtthread.h"

#include "app_process.h"

#include "drv_buzzer.h"
#include "drv_tm1640.h"
#include "drv_relay.h"
#include "drv_hlw8012.h"

#include "drv_uart.h"

typedef enum{
    DEDUCT = 0,
    RECHARGE,
}Card_work_state;

typedef enum{
    NO_STA = 0,
    SUCCESS_STA,
    NO_MONEY_STA,
    ILLEGAL_STA
}Card_sta;

extern rt_sem_t card_recv_sem;

extern void swipe_noreponse_over_time(void);
extern void online_card_over_time(struct disp_t *pdisp);
extern void create_online_card_dynamic_thread(void);
extern Card_sta get_card_deduction_state(void);
extern Card_work_state get_card_work_state(void);

extern uint8_t get_card_swipe_times(void);
extern uint16_t get_card_balance(void);
extern uint16_t get_card_totalAmount(void);
extern uint32_t get_card_serial_num(void);

extern uint8_t get_local_swipe_state(void);
extern uint16_t get_last_card_amount(void);
extern uint8_t get_ic_card_swiped(void);
extern uint8_t get_remote_charge_state(void);
extern uint8_t get_local_card_swipe_state(void);
extern uint8_t get_online_card_swipe_state(void);
extern uint8_t get_card_exit_recovery_state(void);
extern uint8_t get_card_recovery_balance_state(void);
extern uint8_t get_card_exit_recovery_balance_state(void);

extern void set_last_card_amount(uint16_t temp);
extern void set_card_last_serialnum(uint32_t temp);
extern void set_ic_card_swiped(uint8_t sta);
extern void set_remote_charge_state(uint8_t sta);
extern void set_card_work_state(Card_work_state sta);
extern void set_local_card_state(uint8_t sta);
extern void set_online_card_state(uint8_t sta);    
extern void set_card_balance(uint16_t temp);
extern void set_card_deduction_state(Card_sta state);
extern void set_local_card_swipe_state(uint8_t sta);
extern void set_online_card_swipe_state(uint8_t sta);
extern void set_card_exti_recovery_state(uint8_t sta);
extern void set_card_totalAmount(uint16_t temp);
extern void set_card_swipe_times(uint16_t temp);
extern void set_card_last_max_charge_time(uint16_t temp);


#endif
