#include "app_smartcard.h"
#include "app_process.h"
#include "app_data.h"
#include "app_voice.h"

#include "drv_uart.h"
#include "app_powerdetect.h"

struct Ic_card_t
{
    uint8_t isLocalSwipe : 1;
    uint8_t isOnlineSwipe : 1;
    uint8_t isRecoveryBalance : 1;
    uint8_t isExitRecoveryBalance : 1;
    uint8_t isRecvRemoteComplete : 1;
    uint8_t isRecvRemoteOverTime : 1;
    uint8_t isControlLocal : 1;
    uint8_t isControlOnline : 1;

    uint8_t isSwiped : 1;
    uint8_t isLocalSwipeState : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;

    uint8_t state;
    uint8_t swipeTimes;
    uint8_t deduction_state;
    //	uint8_t over_time_cnt;

    uint32_t serialNum;
    uint32_t last_serialNum;
    uint16_t balance;
    uint16_t lastCardBalance;
    uint16_t totalSwipeAmount;
    uint16_t max_charge_time;
    uint16_t swipe_amount;
    uint16_t last_card_amount;
};
static struct Ic_card_t ic_card;

uint8_t remote_charge_state;

uint8_t get_local_swipe_state(void)
{
    return ic_card.isLocalSwipeState;
}

uint16_t get_last_card_amount(void)
{
    return ic_card.last_card_amount;
}

uint8_t get_ic_card_swiped(void)
{
    return ic_card.isSwiped;
}

uint8_t get_remote_charge_state(void)
{
    return remote_charge_state;
}
// 获取对卡片的操作状态,充值或者消费
Card_work_state get_card_work_state(void)
{
    return ic_card.state;
}

// 获取刷卡后的扣费状态
Card_sta get_card_deduction_state(void)
{
    return ic_card.deduction_state;
}

// 获取卡片的序列号
uint32_t get_card_serial_num(void)
{
    return ic_card.serialNum;
}

// 获取卡片余额
uint16_t get_card_balance(void)
{
    return ic_card.last_card_amount;
}

// 获取总的卡片余额
uint16_t get_card_totalAmount(void)
{
    return ic_card.totalSwipeAmount;
}

// 获取刷卡次数
uint8_t get_card_swipe_times(void)
{
    return ic_card.swipeTimes;
}

// 获取刷卡状态
uint8_t get_local_card_swipe_state(void)
{
    return ic_card.isLocalSwipe;
}

// 获取刷卡状态
uint8_t get_online_card_swipe_state(void)
{
    return ic_card.isOnlineSwipe;
}

// 获取余额回收状态
uint8_t get_card_recovery_balance_state(void)
{
    return ic_card.isRecoveryBalance;
}

// 获取余额回收后退出刷卡界面的状态
uint8_t get_card_exit_recovery_balance_state(void)
{
    return ic_card.isExitRecoveryBalance;
}

// 设置卡片的操作状态,充值或者消费
void set_card_work_state(Card_work_state sta)
{
    ic_card.state = sta;
}

// 获取刷卡后的扣费状态
void set_card_deduction_state(Card_sta state)
{
    ic_card.deduction_state = state;
}

// 设置刷卡标记
void set_local_card_swipe_state(uint8_t sta)
{
    if (sta != 0)
        ic_card.isLocalSwipe = 1;
    else
        ic_card.isLocalSwipe = 0;
}

// 设置刷卡标记
void set_online_card_swipe_state(uint8_t sta)
{
    if (sta != 0)
        ic_card.isOnlineSwipe = 1;
    else
        ic_card.isOnlineSwipe = 0;
}

// 设置退出余额回收标记
void set_card_exti_recovery_state(uint8_t sta)
{
    if (sta != 0)
        ic_card.isExitRecoveryBalance = 1;
    else
        ic_card.isExitRecoveryBalance = 0;
}

// 设置总的卡片余额
void set_card_totalAmount(uint16_t temp)
{
    ic_card.totalSwipeAmount = temp;
}

// 设置刷卡次数
void set_card_swipe_times(uint16_t temp)
{
    ic_card.swipeTimes = temp;
}

// 设置卡片余额
void set_card_balance(uint16_t temp)
{
    ic_card.last_card_amount = temp;
}

// 设置刷卡后的充电时间
void set_card_last_max_charge_time(uint16_t temp)
{
    ic_card.max_charge_time = temp;
}

void set_local_card_state(uint8_t sta)
{
    ic_card.isControlLocal = sta;
}

void set_online_card_state(uint8_t sta)
{
    ic_card.isControlOnline = sta;
}

void set_remote_charge_state(uint8_t sta)
{
    remote_charge_state = sta;
}

void set_ic_card_swiped(uint8_t sta)
{
    ic_card.isSwiped = sta;
}

void set_card_last_serialnum(uint32_t temp)
{
    ic_card.last_serialNum = temp;
}

void set_last_card_amount(uint16_t temp)
{
    ic_card.last_card_amount = temp;
}

void swipe_noreponse_over_time(void)
{
    if (ic_card.isLocalSwipe || ic_card.isOnlineSwipe)
    {
        //		if(++ic_card.over_time_cnt >= 30)
        //		{
        //			ic_card.over_time_cnt = 0;
        ic_card.isLocalSwipe = 0;
        ic_card.isOnlineSwipe = 0;
        ic_card.lastCardBalance = 0;
        ic_card.last_card_amount = 0;
        ic_card.last_serialNum = 0;
        ic_card.max_charge_time = 0;
        ic_card.serialNum = 0;
        ic_card.swipeTimes = 0;
        ic_card.swipe_amount = 0;
        ic_card.state = 0;
        //		}
    }
}

void online_card_over_time(struct disp_t *pdisp)
{
    static uint8_t over_time_cnt = 0;
    static uint8_t dot_pos = 0;

    if (ic_card.isRecvRemoteOverTime != 0)
    {
        show_wait_dot(dot_pos);
        if (++dot_pos > 2)
            dot_pos = 0;

        set_back_timer_value(10);

        if (++over_time_cnt >= 10)
        {
            over_time_cnt = 0;
            ic_card.isRecvRemoteOverTime = 0;
            ic_card.isRecvRemoteComplete = 0;
            for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
            {
                main_view(pdisp, i);
            }
        }
    }
    else
    {
        dot_pos = 0;
        over_time_cnt = 0;
    }
}

// 刷卡或者充值
static void ic_card_action(Card_work_state type, uint16_t amount)
{
    uint8_t tx_buf[5];

    tx_buf[0] = 0xAA;
    if (type == DEDUCT)
        tx_buf[1] = 0x55;
    else if (type == RECHARGE)
        tx_buf[1] = 0x66;
    tx_buf[2] = amount >> 8;
    tx_buf[3] = amount & 0xff;
    tx_buf[4] = chk_xrl(tx_buf, 4);

    // 发送数据
    uart_send_data(&huart2, tx_buf, 5);
    //    rt_thread_delay(5);
}

// 刷卡执行程序
static void local_card_action(void *arg)
{
    t_voice_data voice_data;

    static uint16_t balance = 0, last_balance = 0;
    uint16_t i, temp = 0, a, b, c;
    static uint8_t error_port = 0;
    uint8_t flag = 0;

    if (ic_card.isExitRecoveryBalance == 0)
    {
        if (ic_card.isRecoveryBalance != 0)
        {
            // 检查是否余额回收成功
            if (ic_card.balance == last_balance)
            {
                ic_card_action(RECHARGE, balance);
            }
            else
            {
                // 回收完成，语音提示
                COIN_ENABLE;
                // 启用在线卡
                set_online_card_state(0);

                ic_card.swipeTimes = 0;
                ic_card.max_charge_time = 0;
                ic_card.isRecoveryBalance = 0;
                ic_card.isExitRecoveryBalance = 1;
                ic_card.last_serialNum = 0;
                ic_card.lastCardBalance = ic_card.balance;
                // 记录总的刷卡金额,记录的金额 - 回收的金额 = 有效的刷卡金额
                ic_card.totalSwipeAmount -= balance;
                balance = 0;

                seg_clear();
                struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);
                // 显示刷卡金额
                pdisp->type = Parameters;
                pdisp->addr = 0;
                pdisp->data = 0;
                pdisp->point = No_Point;
                pdisp->hide = No_Hide;
                auto_show_digitaltube(pdisp);
                // 显示卡片里的余额
                if (ic_card.balance > 999)
                {
                    pdisp->data = ic_card.balance / 10;
                    pdisp->point = No_Point;
                }
                else
                {
                    pdisp->data = ic_card.balance;
                    pdisp->point = Pos_1;
                }
                pdisp->type = Parameters;
                pdisp->addr = 1;
                pdisp->hide = No_Hide;
                auto_show_digitaltube(pdisp);

                rt_free_align(pdisp);

                //                speeker(Voice_Commd_Balance_Recovery_Successfully);
                voice_data.type = Voice_Commd_Balance_Recovery_Successfully;
                voice_data.power = 0;
                voice_data.time = 0;
                voice_data.balance = 0;
                spell_voice(&voice_data);

                ic_card.last_card_amount = 0;
                //                stop_charge_port(error_port);
                if (get_selfstop_state() != 0)
                {
                    stop_charge_port(error_port);
                }
                else
                {
                    for (i = 0; i < TOTAL_PORTS_NUM; i++)
                    {
                        if (get_charge_recheck_state(i) != 0 || get_charge_reconnect_state(i) != 0)
                            stop_charge_port(i);
                    }
                }

                set_back_timer_value(10);
                set_stop_disp_flag(1);
                // 保存回收余额后的卡片金额
                if (ic_card.totalSwipeAmount >= 0xffff)
                    ic_card.totalSwipeAmount = 0;
                save_sys_parameters(15, ic_card.totalSwipeAmount);
            }
        }
        else
        {
            if (ic_card.serialNum != ic_card.last_serialNum)
            {
                ic_card.lastCardBalance = 0;
                ic_card.swipeTimes = 0;

                temp = get_port_error_state();
                // 当前卡刷卡之后报错,换另外一张卡刷,不处理,直接跳出程序
                // 判断当前状态是不是处在刚刷完卡开始报错的阶段
                if (get_rechoose_state() == 0)
                {
                    // 检查当前有没有出现刷了卡报错的端口,如果有则跳出当前程序
                    for (i = 0; i < TOTAL_PORTS_NUM; i++)
                    {
                        if (((temp & (1 << i)) != 0) && (get_charge_paytype(i) == LOCAL_SWIPE_TYPE))
                        {
                            flag++;
                        }
                    }
                    if (flag != 0)
                    {
                        return;
                    }
                }

                // 保存当前刷卡的卡的SN码
                ic_card.last_serialNum = ic_card.serialNum;

                // 判断刷了卡后有没有被执行充电的操作,如果没有执行,则使能投币,本地卡和在线卡的功能
                if (ic_card.isSwiped != 0)
                {
                    COIN_ENABLE;
                    set_local_card_state(0);
                    set_online_card_state(0);

                    set_rechoose_state(1);
                }
                ic_card.isSwiped = 1;

                // 判断当前状态是否已经跳出报错的阶段,处于回显金额的状态
                if (get_rechoose_state() != 0)
                {
                    set_rechoose_state(0);
                    // 检查当前有哪些端口处于报错的状态,有报错,并且是本地卡或者投币之后产生的,则清除这些端口的状态
                    for (i = 0; i < TOTAL_PORTS_NUM; i++)
                    {
                        if ((temp & (1 << i)) != 0)
                        {
                            flag++;
                            set_port_error_state(false, i);

                            if (get_charge_paytype(i) == LOCAL_SWIPE_TYPE || get_charge_paytype(i) == COIN_TYPE)
                            {
                                set_detect_state(i, 0);

                                set_charge_reset(i);

                                relay_write(i, GPIO_PIN_RESET);

                                init_charge_rom(i, D_NORMAL_MODE);
                                reset_filter_data(i);
                            }
                        }
                    }
                    if (flag != 0)
                    {
                        ic_card.swipeTimes = 0;
                        set_coin_coins(0);
                        //                        set_fail_back_flag(0);
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR, get_charge_enable_state());
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 2, get_charge_local_card_paytype_state());
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 4, get_charge_online_card_paytype_state());
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 6, get_charge_coin_paytype_state());
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 8, get_charge_remote_paytype_state());
                    }
                }
                if (get_selfstop_state() != 0)
                {
                    for (i = 0; i < TOTAL_PORTS_NUM; i++)
                    {
                        if (get_manual_recovery_amount_flag(i) != 0 && ic_card.serialNum == get_port_card_serilanum(i))
                        {
                            set_manual_recovery_amount_flag(i, 0);
                            // 判断是否开启余额回收功能,如果开启,则可以回收余额
                            if (get_allowrecovery_state() != 0)
                            {
                                ic_card.last_card_amount = get_port_card_last_amount(i);
                                ic_card.balance = ic_card.last_card_amount;
                                set_charge_recheck_state(i, 1);
                                set_charge_reconnect_state(i, 1);
                            }
                            else
                            {
                                set_charge_reset(i);

                                set_detect_state(i, 0);

                                relay_write(i, GPIO_PIN_RESET);

                                init_charge_rom(i, D_NORMAL_MODE);
                                reset_filter_data(i);
                            }
                        }
                    }
                }
                else
                {
                    for (i = 0; i < TOTAL_PORTS_NUM; i++)
                    {
                        // 充满自停关闭的情况下,如果充电时间超过5分钟,那么进入余额回收模式
                        temp = get_dynamic_total_charge_time(i) - get_charge_times(i);
                        if (temp > 5)
                        {
                            if (get_charge_paytype(i) == LOCAL_SWIPE_TYPE && ic_card.serialNum == get_port_card_serilanum(i))
                            {
                                ic_card.last_card_amount = get_port_card_last_amount(i);
                                ic_card.balance = ic_card.last_card_amount;
                                set_charge_recheck_state(i, 1);
                                set_charge_reconnect_state(i, 1);
                            }
                        }
                    }
                }
                // 查询当前未连接充电设备需要回收余额的端口
                for (i = 0; i < TOTAL_PORTS_NUM; i++)
                {
                    if (get_charge_paytype(i) == LOCAL_SWIPE_TYPE && ic_card.serialNum == get_port_card_serilanum(i))
                    {
                        if (get_charge_recheck_state(i) != 0 || get_charge_reconnect_state(i) != 0)
                        {
                            set_rechoose_state(0);
                            ic_card.isRecoveryBalance = 1;
                            error_port = i; // 保存故障端口号，用来回收余额

                            last_balance = ic_card.balance;

                            set_back_timer_value(30);
                            set_stop_disp_flag(1);

                            if (get_selfstop_state() != 0)
                            {
                                balance = get_charge_remain_amount(i); // 用来回充的剩余金额
                                break;
                            }
                            else
                            {
                                balance += get_charge_remain_amount(i); // 用来回充的剩余金额
                            }
                        }
                    }
                }
            }
            else
            {
                if (get_selfstop_state() != 0)
                {
                    // 在刚刷完卡,出现报错的情况下
                    temp = get_port_error_state();
                    for (i = 0; i < TOTAL_PORTS_NUM; i++)
                    {
                        if (get_manual_recovery_amount_flag(i) != 0 && ic_card.serialNum == get_port_card_serilanum(i))
                        {
                            set_manual_recovery_amount_flag(i, 0);
                            // 判断是否开启余额回收功能,如果开启,则可以回收余额
                            if (get_allowrecovery_state() != 0)
                            {
                                ic_card.last_card_amount = get_port_card_last_amount(i);
                                ic_card.balance = ic_card.last_card_amount;
                                set_charge_recheck_state(i, 1);
                                set_charge_reconnect_state(i, 1);
                            }
                            else
                            {
                                set_charge_reset(i);

                                set_detect_state(i, 0);

                                relay_write(i, GPIO_PIN_RESET);

                                init_charge_rom(i, D_NORMAL_MODE);
                                reset_filter_data(i);
                            }
                        }
                        if ((temp & (1 << i)) != 0)
                        {
                            flag++;
                            set_port_error_state(false, i);

                            if (get_charge_paytype(i) == LOCAL_SWIPE_TYPE)
                            {
                                // 判断是否开启余额回收功能,如果开启,则可以回收余额
                                if (get_allowrecovery_state() != 0)
                                {
                                    if (i != get_last_charge_port())
                                    {
                                        set_charge_reset(i);

                                        set_detect_state(i, 0);

                                        relay_write(i, GPIO_PIN_RESET);

                                        init_charge_rom(i, D_NORMAL_MODE);
                                        reset_filter_data(i);
                                    }
                                    else
                                    {
                                        //                                    set_port_error_state(true, i);
                                        set_charge_recheck_state(i, 1);
                                        set_charge_reconnect_state(i, 1);
                                    }
                                }
                                else
                                {
                                    set_charge_reset(i);

                                    set_detect_state(i, 0);

                                    relay_write(i, GPIO_PIN_RESET);

                                    init_charge_rom(i, D_NORMAL_MODE);
                                    reset_filter_data(i);
                                }
                            }
                            else if (get_charge_paytype(i) == COIN_TYPE)
                            {
                                set_charge_reset(i);

                                set_detect_state(i, 0);

                                relay_write(i, GPIO_PIN_RESET);

                                init_charge_rom(i, D_NORMAL_MODE);
                                reset_filter_data(i);
                            }
                        }
                    }
                    if (flag != 0)
                    {
                        ic_card.swipeTimes = 0;
                        set_coin_coins(0);
                        //                    set_fail_back_flag(0);
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR, get_charge_enable_state());
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 2, get_charge_local_card_paytype_state());
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 4, get_charge_online_card_paytype_state());
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 6, get_charge_coin_paytype_state());
                        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 8, get_charge_remote_paytype_state());
                    }
                }
                else
                {
                    for (i = 0; i < TOTAL_PORTS_NUM; i++)
                    {
                        // 充满自停关闭的情况下,如果充电时间超过5分钟,那么进入余额回收模式
                        temp = get_dynamic_total_charge_time(i) - get_charge_times(i);
                        if (temp > 5)
                        {
                            if (get_charge_paytype(i) == LOCAL_SWIPE_TYPE && ic_card.serialNum == get_port_card_serilanum(i))
                            {
                                ic_card.last_card_amount = get_port_card_last_amount(i);
                                ic_card.balance = ic_card.last_card_amount;
                                set_charge_recheck_state(i, 1);
                                set_charge_reconnect_state(i, 1);
                            }
                        }
                    }
                }

                // 查询当前未连接充电设备需要回收余额的端口
                for (i = 0; i < TOTAL_PORTS_NUM; i++)
                {
                    if (get_charge_paytype(i) == LOCAL_SWIPE_TYPE && ic_card.serialNum == get_port_card_serilanum(i))
                    {
                        if (get_charge_recheck_state(i) != 0 || get_charge_reconnect_state(i) != 0)
                        {
                            set_rechoose_state(0);
                            ic_card.isRecoveryBalance = 1;
                            error_port = i; // 保存故障端口号，用来回收余额

                            last_balance = ic_card.balance;

                            set_back_timer_value(30);
                            set_stop_disp_flag(1);

                            if (get_selfstop_state() != 0)
                            {
                                balance = get_charge_remain_amount(i); // 用来回充的剩余金额
                                break;
                            }
                            else
                            {
                                balance += get_charge_remain_amount(i); // 用来回充的剩余金额
                            }
                        }
                    }
                }
            }

            if (ic_card.isRecoveryBalance != 0)
            {
                seg_clear();

                struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);
                // 显示待回收的刷卡金额
                if (balance > 999)
                {
                    pdisp->data = balance / 10;
                    pdisp->point = No_Point;
                }
                else
                {
                    pdisp->data = balance;
                    pdisp->point = Pos_1;
                }
                pdisp->addr = 0;
                pdisp->type = Parameters;
                pdisp->hide = No_Hide;
                auto_show_digitaltube(pdisp);
                // 显示卡片里的余额
                if (ic_card.balance > 999)
                {
                    pdisp->data = ic_card.balance / 10;
                    pdisp->point = No_Point;
                }
                else
                {
                    pdisp->data = ic_card.balance;
                    pdisp->point = Pos_1;
                }
                pdisp->addr = 1;
                pdisp->type = Parameters;
                pdisp->hide = No_Hide;
                auto_show_digitaltube(pdisp);

                rt_free_align(pdisp);

                return;
            }

            set_back_timer_value(20);
            set_stop_disp_flag(1);
            if (ic_card.isRecoveryBalance == 0)
            {
                // 超出最大充电时间限制，则不允许再次扣费
                if ((1000 - ic_card.max_charge_time) > get_ic_card_charge_time_value())
                {
                    if (ic_card.lastCardBalance == 0)
                        ic_card.lastCardBalance = ic_card.balance;
                    // 检测是否对卡片扣费成功
                    if (ic_card.balance == ic_card.lastCardBalance)
                    {
                        // 检测剩余的余额是否还能扣，余额不足之后语音提示
                        // 如果扣费金额为0，则直接进行扣费操作
                        if (get_ic_card_deduction_amount() == 0)
                        {
                            ic_card_action(DEDUCT, 0);
                        }
                        else if (ic_card.balance < get_ic_card_deduction_amount())
                        {
                            //                            speeker(Voice_Commd_Insufficient_Balance);
                            voice_data.type = Voice_Commd_Insufficient_Balance;
                            voice_data.power = 0;
                            voice_data.time = 0;
                            voice_data.balance = 0;
                            spell_voice(&voice_data);

                            set_back_timer_value(1);
                            //                            set_stop_disp_flag(0);
                        }
                        else
                        {
                            ic_card_action(DEDUCT, get_ic_card_deduction_amount());
                        }
                    }
                    else
                    {
                        // 扣费完成，语音提示
                        COIN_DISABLE; // 刷卡状态下禁止投币
                        // 禁用本地卡
                        set_online_card_state(1);
                        ic_card.isControlLocal = 1;
                        ic_card.isLocalSwipeState = 1;
                        //						ic_card.over_time_cnt = 0;

                        if (get_error_sta() != 0)
                        {
                            COIN_ENABLE;
                            set_local_card_state(0);
                            set_online_card_state(0);
                            //                            if(get_last_paytype() != LOCAL_SWIPE_TYPE)
                            //                                set_coin_coins(0);
                        }

                        set_coin_coins(0);

                        ic_card.isLocalSwipe = 1;
                        ic_card.last_card_amount = ic_card.balance;

                        // 对单次最大的刷卡次数做限制
                        if (++ic_card.swipeTimes >= 200)
                        {
                            ic_card.swipeTimes = 200;
                        }

                        a = get_ic_card_deduction_amount();
                        b = get_ic_card_charge_time_value();
                        // 计算刷卡金额
                        if (get_rechoose_state() != 0 && (get_last_paytype() == LOCAL_SWIPE_TYPE))
                        {
                            c = get_last_charge_amount();
                            // 修改计算逻辑，处理扣费金额为0的情况
                            if (a == 0)
                            {
                                ic_card.swipe_amount = c;
                                ic_card.swipeTimes = 1; // 当扣费金额为0时，设置刷卡次数为1
                            }
                            else
                            {
                                ic_card.swipe_amount = ic_card.swipeTimes * a + c;
                                ic_card.swipeTimes = ic_card.swipe_amount / a;
                            }
                            //                            set_last_charge_amount(0);
                        }
                        else
                        {
                            //                            set_last_charge_amount(0);
                            set_coin_enable_state(0);
                            set_last_paytype(NO_TYPE);
                            // 修改计算逻辑，处理扣费金额为0的情况
                            if (a == 0)
                            {
                                ic_card.swipe_amount = 0;
                                ic_card.swipeTimes = 1; // 当扣费金额为0时，设置刷卡次数为1
                            }
                            else
                            {
                                ic_card.swipe_amount = ic_card.swipeTimes * a;
                            }
                        }
                        set_rechoose_state(0);
                        // 计算有效刷卡后对应的可充电时间
                        ic_card.max_charge_time += b;

                        seg_clear();

                        struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);
                        // 显示待回收的刷卡金额
                        if (ic_card.swipe_amount > 999)
                        {
                            pdisp->data = ic_card.swipe_amount / 10;
                            pdisp->point = No_Point;
                        }
                        else
                        {
                            pdisp->data = ic_card.swipe_amount;
                            pdisp->point = Pos_1;
                        }
                        pdisp->addr = 0;
                        pdisp->type = Parameters;
                        pdisp->hide = No_Hide;
                        auto_show_digitaltube(pdisp);
                        // 显示卡片里的余额
                        if (ic_card.balance > 999)
                        {
                            pdisp->data = ic_card.balance / 10;
                            pdisp->point = No_Point;
                        }
                        else
                        {
                            pdisp->data = ic_card.balance;
                            pdisp->point = Pos_1;
                        }
                        pdisp->addr = 1;
                        pdisp->type = Parameters;
                        pdisp->hide = No_Hide;
                        auto_show_digitaltube(pdisp);

                        rt_free_align(pdisp);

                        // 报卡片余额，然后再报刷卡成功
                        //                         speeker(Voice_Commd_Current_Current_Card_Balance);
                        //                         rt_thread_mdelay(1500);
                        //                         spell_voice(SPELL_BALANCE_TYPE, ic_card.balance);
                        //                         rt_thread_mdelay(500);
                        //                         speeker(Voice_Commd_Swipe_Successfully);
                        //                         rt_thread_mdelay(2500);

                        voice_data.type = SPELL_BALANCE_TYPE;
                        voice_data.power = 0;
                        voice_data.time = 0;
                        voice_data.balance = ic_card.balance;
                        spell_voice(&voice_data);

                        rt_thread_mdelay(1500);
                        ic_card.isControlLocal = 0;
                        ic_card.isLocalSwipeState = 0;
                    }
                }
                // 对上一次的余额进行保存
                ic_card.lastCardBalance = ic_card.balance;
            }
        }
    }
}

static void online_card_action(void *arg)
{
    t_voice_data voice_data;

    uint16_t temp = 0;
    uint8_t i, flag = 0;

    ic_card.isRecvRemoteOverTime = 0;
    ic_card.isRecvRemoteComplete = 0;
    rt_thread_delay(1);

    switch (ic_card.deduction_state)
    {
    case SUCCESS_STA:
        // 刷卡状态下禁止投币
        COIN_DISABLE;
        // 禁用本地卡
        set_local_card_state(1);

        set_rechoose_state(0);
        temp = get_port_error_state();
        for (i = 0; i < TOTAL_PORTS_NUM; i++)
        {
            if ((temp & (1 << i)) != 0)
            {
                set_port_error_state(false, i);
                flag++;

                if (get_charge_paytype(i) == LOCAL_SWIPE_TYPE || get_charge_paytype(i) == COIN_TYPE)
                {
                    set_charge_reset(i);

                    relay_write(i, GPIO_PIN_RESET);

                    init_charge_rom(i, D_NORMAL_MODE);
                    reset_filter_data(i);
                }
            }
        }
        if (flag != 0)
        {
            //                set_fail_back_flag(0);
            save_parameters(OUTAGE_FLAG_16BIT_ADDR, get_charge_enable_state());
            save_parameters(OUTAGE_FLAG_16BIT_ADDR + 2, get_charge_local_card_paytype_state());
            save_parameters(OUTAGE_FLAG_16BIT_ADDR + 4, get_charge_online_card_paytype_state());
            save_parameters(OUTAGE_FLAG_16BIT_ADDR + 6, get_charge_coin_paytype_state());
            save_parameters(OUTAGE_FLAG_16BIT_ADDR + 8, get_charge_remote_paytype_state());
        }

        set_coin_coins(0);
        // 设置返回主界面时间
        set_stop_disp_flag(1);
        set_back_timer_value(20);
        ic_card.isOnlineSwipe = 1;
        //		ic_card.over_time_cnt = 0;
        ic_card.balance = ic_card.last_card_amount;
        ic_card.last_serialNum = ic_card.serialNum;

        // 对单次最大的刷卡次数做限制
        if (++ic_card.swipeTimes >= 200)
        {
            ic_card.swipeTimes = 200;
        }
        // 计算刷卡金额
        // 修改计算逻辑，处理扣费金额为0的情况
        uint16_t deduction_amount = get_ic_card_deduction_amount();
        if (deduction_amount == 0)
        {
            ic_card.swipe_amount = 0;
            ic_card.swipeTimes = 1; // 当扣费金额为0时，设置刷卡次数为1
        }
        else
        {
            ic_card.swipe_amount = ic_card.swipeTimes * deduction_amount;
        }
        // 计算有效刷卡后对应的可充电时间
        ic_card.max_charge_time += get_ic_card_charge_time_value();

        seg_clear();

        struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);
        // 显示刷卡金额
        if (ic_card.swipe_amount > 999)
        {
            pdisp->data = ic_card.swipe_amount / 10;
            pdisp->point = No_Point;
        }
        else
        {
            pdisp->data = ic_card.swipe_amount;
            pdisp->point = Pos_1;
        }
        pdisp->addr = 0;
        pdisp->type = Parameters;
        pdisp->hide = No_Hide;
        auto_show_digitaltube(pdisp);
        // 显示卡片里的余额
        if (ic_card.balance > 9999)
        {
            pdisp->data = ic_card.balance / 10;
            pdisp->point = No_Point;
        }
        else
        {
            pdisp->data = ic_card.balance;
            pdisp->point = Pos_4;
        }
        pdisp->addr = 1;
        pdisp->type = Check_Amount;
        pdisp->hide = No_Hide;
        auto_show_digitaltube(pdisp);

        rt_free_align(pdisp);

        // 报刷卡成功，然后再报卡片余额
        //         speeker(Voice_Commd_Swipe_Successfully);
        //         rt_thread_mdelay(5000);
        //         speeker(Voice_Commd_Current_Current_Card_Balance);
        //         rt_thread_mdelay(2000);
        //         spell_voice(SPELL_BALANCE_TYPE, ic_card.balance);

        voice_data.type = SPELL_BALANCE_TYPE;
        voice_data.power = 0;
        voice_data.time = 0;
        voice_data.balance = ic_card.balance;
        spell_voice(&voice_data);
        break;
    case ILLEGAL_STA:
        break;
    case NO_MONEY_STA:
        set_back_timer_value(1);
        //        speeker(Voice_Commd_Insufficient_Balance);
        voice_data.type = Voice_Commd_Insufficient_Balance;
        voice_data.power = 0;
        voice_data.time = 0;
        voice_data.balance = 0;
        spell_voice(&voice_data);
        break;
    default:
        break;
    }
}

// 创建动态线程控制块
#define CARD_THREAD_STACK_SIZE 600
static rt_thread_t local_card = RT_NULL;
void create_local_card_dynamic_thread(void)
{
    /* 创建线程 1，名称是 thread1，入口是 thread1_entry*/
    local_card = rt_thread_create("local_card",
                                  local_card_action,
                                  RT_NULL,
                                  CARD_THREAD_STACK_SIZE,
                                  2,
                                  5);

    /* 如果获得线程控制块，启动这个线程 */
    if (local_card != RT_NULL)
    {
        //        rt_kprintf("create transmit dynamic thread sucess \r\n");
        rt_thread_startup(local_card);
    }
}

// 创建动态线程控制块
static rt_thread_t online_card = RT_NULL;
void create_online_card_dynamic_thread(void)
{
    /* 创建线程 1，名称是 thread1，入口是 thread1_entry*/
    online_card = rt_thread_create("online_card",
                                   online_card_action,
                                   RT_NULL,
                                   CARD_THREAD_STACK_SIZE,
                                   2,
                                   5);

    /* 如果获得线程控制块，启动这个线程 */
    if (online_card != RT_NULL)
    {
        //        rt_kprintf("create transmit dynamic thread sucess \r\n");
        rt_thread_startup(online_card);
    }
}

static void card_entry(void *arg)
{
    uint8_t i, sum_code = 0;
    uint8_t recv_buf[USART2_RX_BUF_SIZE];
    rt_err_t result;

    // 开启串口中断
    enable_usart_irq();

    while (1)
    {
        if (rt_sem_take(card_recv_sem, RT_WAITING_FOREVER) == RT_EOK)
        {
            //            uint8_t *recv_buf = rt_malloc_align(sizeof(uint8_t)*USART2_RX_BUF_SIZE, 4);

            if (get_recevie_buf(&huart2, recv_buf) == RT_EOK) // 接收到数据
                sum_code = chk_xrl(recv_buf, 8);
            if (sum_code == recv_buf[8])
            {
                // 数据校验成功,执行扣费或者回收
                if (recv_buf[0] == 0x55 && recv_buf[1] == 0xAA)
                {
                    // 得到卡片的id码
                    ic_card.serialNum = recv_buf[2] | (recv_buf[3] << 8) | (recv_buf[4] << 16) | (recv_buf[5] << 24);
                    // 获取卡片里的余额
                    ic_card.balance = recv_buf[6] << 8 | recv_buf[7];

                    if (ic_card.balance == 0xAA33)
                    {
                        if (ic_card.isRecvRemoteComplete == 0 && ic_card.isControlOnline == 0)
                        {
                            if (ic_card.max_charge_time < 1000)
                            {
                                ic_card.state = DEDUCT;
                                ic_card.isRecvRemoteComplete = 1;
                                ic_card.isRecvRemoteOverTime = 1;
                                set_stop_disp_flag(1);
                                set_back_timer_value(10);
                                seg_clear();
                                set_upload_all_timer(0);
                                send_gprs_mb_cmd(0x10, 0);
                            }
                        }
                    }
                    else
                    {
                        // 不处理正常金额的卡
                        if (ic_card.balance > 3000)
                        {
                            return;
                        }
                        if (ic_card.isControlLocal == 0)
                        {
                            // 创建动态处理线程,处理完成自动删除,回收内存
                            create_local_card_dynamic_thread();
                        }
                    }
                }
            }
        }
    }
}

#define CARD_THREAD_PRIORITY 3
#define CARD_THREAD_TIMESLICE 1

ALIGN(RT_ALIGN_SIZE)
static char card_stack[250];
static struct rt_thread card;
/* 指向信号量的指针 */
rt_sem_t card_recv_sem = RT_NULL;
int card_thread(void)
{

    /* 创建一个动态信号量，初始值是 0 */
    card_recv_sem = rt_sem_create("card_recv_sem", 0, RT_IPC_FLAG_FIFO);
    if (card_recv_sem == RT_NULL)
    {
        //        rt_kprintf("create card_recv_sem failed.\n");
        return RT_ERROR;
    }

    rt_thread_init(&card,
                   "card",
                   card_entry,
                   RT_NULL,
                   &card_stack[0],
                   sizeof(card_stack),
                   CARD_THREAD_PRIORITY,
                   CARD_THREAD_TIMESLICE);
    rt_thread_startup(&card);

    return RT_EOK;
}
INIT_APP_EXPORT(card_thread);
