#ifndef __DRV_TM1640_H__
#define __DRV_TM1640_H__

#include "stm32f0xx.h"
#include "rtthread.h"
#include "drv_common.h"

#define DISP_LINE_NUM	 10
#define DISP_ERROR_NUM   12
#define DISP_HIGH_NUM    13

#define TM1640_CLK_HIGH(x)   \
    do                  \
    {                   \
        if(x == 1)      \
        {               \
            GPIOC->BSRR = GPIO_PIN_1; \
        }               \
        else if(x == 2) \
        {               \
            GPIOF->BSRR = GPIO_PIN_5; \
        }               \
    }while(0)           
    
#define TM1640_CLK_LOW(x)   \
    do                  \
    {                   \
        if(x == 1)      \
        {               \
            GPIOC->BRR = GPIO_PIN_1; \
        }               \
        else if(x == 2) \
        {               \
            GPIOF->BRR = GPIO_PIN_5; \
        }               \
    }while(0)           

#define TM1640_DAT_HIGH(x)   \
    do                  \
    {                   \
        if(x == 1)      \
        {               \
            GPIOC->BSRR = GPIO_PIN_0; \
        }               \
        else if(x == 2) \
        {               \
            GPIOF->BSRR = GPIO_PIN_4; \
        }               \
    }while(0)               
    
#define TM1640_DAT_LOW(x)   \
    do                  \
    {                   \
        if(x == 1)      \
        {               \
            GPIOC->BRR = GPIO_PIN_0; \
        }               \
        else if(x == 2) \
        {               \
            GPIOF->BRR = GPIO_PIN_4; \
        }               \
    }while(0)         
    
typedef enum{
    No_Hide = 6,
    Hidden_All,
    Hidden_Pos_2and3,
}hide_type;

typedef enum{
    Pos_0 = 0,
    Pos_1,
    Pos_2,
    Pos_3,
    Pos_4,
	Pos_5,
    Pos_6,
    Pos_7,
    Pos_8,
	No_Point = 0xff
}point_type;

typedef enum{
    Idle = 0,
    Error,
    Set,
    Check_Amount,
    Check_power,
    Check_temperture,
	Check_Version,
    Parameters
}disp_type;

struct disp_t{
    uint32_t data;
    uint8_t addr;
    disp_type type;
    point_type point;
    hide_type hide;
};

extern int tm1640_init(void);
extern void show_wait_dot(uint8_t pos_addr);
extern void seg_clear(void);
extern void auto_show_digitaltube(struct disp_t *pdisp);
//extern void auto_show_digitaltube(uint8_t type, uint8_t wraddr, uint32_t data, uint8_t point, uint8_t hide);
extern void show_circle(uint8_t group_num, uint8_t addr);
#endif
