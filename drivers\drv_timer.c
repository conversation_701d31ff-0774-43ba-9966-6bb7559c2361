#include "drv_timer.h"
#include "drv_common.h"

static TIM_HandleTypeDef htim1;

void HAL_TIM_Base_MspInit(TIM_HandleTypeDef *tim_baseHandle)
{

    if (tim_baseHandle->Instance == TIM1)
    {
        /* TIM1 clock enable */
        __HAL_RCC_TIM1_CLK_ENABLE();

        /* TIM1 interrupt Init */
        HAL_NVIC_SetPriority(TIM1_BRK_UP_TRG_COM_IRQn, 3, 1);
        HAL_NVIC_EnableIRQ(TIM1_BRK_UP_TRG_COM_IRQn);
    }
}

void HAL_TIM_Base_MspDeInit(TIM_HandleTypeDef *tim_baseHandle)
{

    if (tim_baseHandle->Instance == TIM1)
    {
        /* Peripheral clock disable */
        __HAL_RCC_TIM1_CLK_DISABLE();

        /* TIM1 interrupt Deinit */
        HAL_NVIC_DisableIRQ(TIM1_BRK_UP_TRG_COM_IRQn);
    }
}

static void timer1_init(void)
{
    uint32_t prescaler_value = 0;

    htim1.Instance = TIM1;
    htim1.Init.Period = 1000 - 1;
    htim1.Init.Prescaler = 48 - 1;
    htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim1.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim1.Init.RepetitionCounter = 0;
    htim1.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    if (HAL_TIM_Base_Init(&htim1) != HAL_OK)
    {
        Error_Handler();
    }
    else
    {
        /* clear update flag */
        __HAL_TIM_CLEAR_FLAG(&htim1, TIM_FLAG_UPDATE);
        /* enable update request source */
        __HAL_TIM_URS_ENABLE(&htim1);

        htim1.Instance->CR1 |= TIM_OPMODE_REPETITIVE;
        HAL_TIM_Base_Start_IT(&htim1);
        HAL_TIM_Base_Start(&htim1);
    }
}

int timer_init(void)
{
    timer1_init();

    return RT_EOK;
}
INIT_BOARD_EXPORT(timer_init);

void TIM1_BRK_UP_TRG_COM_IRQHandler(void)
{
    /* enter interrupt */
    rt_interrupt_enter();
    HAL_TIM_IRQHandler(&htim1);
    /* leave interrupt */
    rt_interrupt_leave();
}
