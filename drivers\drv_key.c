#include "drv_key.h"
#include "stdbool.h"

#define row 0
#define col 1

#define ROW_NUM 3
#define COL_NUM 2

//typedef struct
//{
//    GPIO_TypeDef* GPIOX;
//    uint16_t GPIO_Pin;
//}KGPT[2][4]; //4*4
//static const KGPT KPIN={ //只需要在下面填写横竖行的IO口和管脚   IO口和管脚都可以随意定义 无需在一个IO口 管脚也无需按顺序
//            {{GPIOB,GPIO_PIN_3} ,{GPIOB,GPIO_PIN_4}, {GPIOB,GPIO_PIN_5}},//row 横行
//            {{GPIOC,GPIO_PIN_12},{GPIOD,GPIO_PIN_2}}  //col 竖行
//};

//static int KEY_Init(void)
//{
//    uint8_t i;
//
//    GPIO_InitTypeDef GPIO_InitStructure;

//    for(i = 0; i < ROW_NUM; i ++)
//    {
//        //初始化row
//        GPIO_InitStructure.Pin = KPIN[row][i].GPIO_Pin;
//        GPIO_InitStructure.Mode = GPIO_MODE_OUTPUT_PP;
//        GPIO_InitStructure.Pull = GPIO_NOPULL;
//        GPIO_InitStructure.Speed = GPIO_SPEED_FREQ_HIGH;
//        HAL_GPIO_Init(KPIN[row][i].GPIOX, &GPIO_InitStructure);
//
//        HAL_GPIO_WritePin(KPIN[row][i].GPIOX, KPIN[row][i].GPIO_Pin, GPIO_PIN_RESET);
//    }
//    for(i = 0; i < COL_NUM; i ++)
//    {
//        //初始化col
//        GPIO_InitStructure.Pin = KPIN[col][i].GPIO_Pin;
//        GPIO_InitStructure.Mode = GPIO_MODE_INPUT;
//        GPIO_InitStructure.Pull = GPIO_PULLUP;
//        GPIO_InitStructure.Speed = GPIO_SPEED_FREQ_HIGH;
//        HAL_GPIO_Init(KPIN[col][i].GPIOX, &GPIO_InitStructure);
//    }
//
//    return RT_EOK;
//}
//INIT_BOARD_EXPORT(KEY_Init);

//static uint8_t row_scan(GPIO_TypeDef* GPIOX, uint16_t colPin, uint8_t colIndex)
//{
//    uint8_t i;

//    for(i = 0; i < ROW_NUM; i ++)
//    {
//        HAL_GPIO_WritePin(KPIN[row][i].GPIOX, KPIN[row][i].GPIO_Pin, GPIO_PIN_SET);//每个行置1
//        if((HAL_GPIO_ReadPin(GPIOX, colPin) == 1)) //如果列也变了 行的值就知道了 为 i
//        {
//            HAL_GPIO_WritePin(KPIN[row][i].GPIOX, KPIN[row][i].GPIO_Pin, GPIO_PIN_RESET); //行恢复 置0
//            return colIndex+i*4+1;//返回的数据 为1-16 对应4x4键盘的16个键
//        }
//        HAL_GPIO_WritePin(KPIN[row][i].GPIOX,KPIN[row][i].GPIO_Pin, GPIO_PIN_RESET);     //行恢复 置0
//    }
//    return 0;
//}

//static uint8_t scan_keyboard(void)
//{
//    uint8_t i,keyvalue;

//    for(i = 0; i < COL_NUM; i ++)
//    {
//        if(HAL_GPIO_ReadPin(KPIN[col][i].GPIOX, KPIN[col][i].GPIO_Pin) == 0)//检测列 列值为 i
//        {
//            keyvalue = row_scan(KPIN[col][i].GPIOX, KPIN[col][i].GPIO_Pin,i);//检测行 取键值
////            while(HAL_GPIO_ReadPin(KPIN[col][i].GPIOX, KPIN[col][i].GPIO_Pin) == 0);
//            return keyvalue;//返回键值
//        }
//    }
//    return 0;
//}

static uint8_t Get_Key(void)
{
	//    uint8_t key_temp = 0;

	//    key_temp = scan_keyboard();

	//    if(key_temp == 9)
	//        return KEY1_DOWN;
	//    else if(key_temp == 1)
	//        return KEY2_DOWN;
	//    else if(key_temp == 5)
	//        return KEY3_DOWN;
	//    else if(key_temp == 10)
	//        return KEY4_DOWN;
	//    else if(key_temp == 2)
	//        return KEY5_DOWN;
	//    else if(key_temp == 6)
	//        return KEY6_DOWN;
	//    else if (KEY1 == GPIO_PIN_RESET)
	//        return KEY11_DOWN;
	//    else if (KEY2 == GPIO_PIN_RESET)
	//        return KEY12_DOWN;
	//    else if (KEY3 == GPIO_PIN_RESET)
	//        return KEY13_DOWN;
	//    else if (COIN == GPIO_PIN_RESET)
	//        return KEY14_DOWN;

	if (KEY1 == GPIO_PIN_RESET)
		return KEY11_DOWN;
	else if (KEY2 == GPIO_PIN_RESET)
		return KEY12_DOWN;
	else if (KEY3 == GPIO_PIN_RESET)
		return KEY13_DOWN;
	else if (KEY4 == GPIO_PIN_RESET)
		return KEY1_DOWN;
	else if (KEY5 == GPIO_PIN_RESET)
		return KEY2_DOWN;
	else if (KEY6 == GPIO_PIN_RESET)
		return KEY3_DOWN;
	else if (KEY7 == GPIO_PIN_RESET)
		return KEY4_DOWN;
	else if (KEY8 == GPIO_PIN_RESET)
		return KEY5_DOWN;
	else if (KEY9 == GPIO_PIN_RESET)
		return KEY6_DOWN;
	else if (KEY10 == GPIO_PIN_RESET)
		return KEY7_DOWN;
	else if (KEY11 == GPIO_PIN_RESET)
		return KEY8_DOWN;
	else if (KEY12 == GPIO_PIN_RESET)
		return KEY9_DOWN;
	else if (KEY13 == GPIO_PIN_RESET)
		return KEY10_DOWN;
	else if (COIN == GPIO_PIN_RESET)
		return KEY14_DOWN;

	return NO_KEY;
}

uint8_t Key_Scan(void)
{
	static uint8_t Key_State = 0;	//按键状态
	static uint8_t Key_Prev = 0;	//上一次按键
	static uint16_t Key_Delay = 0;	//按键连发时间
	static bool Key_Series = false; //标志连发开始
	uint8_t Key_Press = NO_KEY;		//按键值
	uint8_t Key_Return = NO_KEY;	//按键返回值

	Key_Press = Get_Key();
	switch (Key_State)
	{
	case 0:						 //按键初始态00
		if (Key_Press != NO_KEY) //有按键按下
		{
			Key_State = 1;		  //转到按键确认
			Key_Prev = Key_Press; //保存按键状态
		}
		break;
	case 1:						   //按键确认态01
		if (Key_Press == Key_Prev) //确认和上次按键相同
		{
			Key_State = 2; //判断按键长按
			//返回按键按下键值,按键按下就响应,如果想弹起来再响应
			//可以在弹起来后再返回按键值
			Key_Return = KEY_DOWN | Key_Prev;
		}
		else //按键抬起,是抖动,不响应按键
		{
			Key_State = 0;
		}
		break;
	case 2:						 //按键释放态10
		if (Key_Press == NO_KEY) //按键释放了
		{
			Key_State = 0;
			Key_Delay = 0;
			Key_Series = false;
			Key_Return = KEY_UP | Key_Prev; //返回按键抬起值
			break;
		}
		if (Key_Press == Key_Prev)
		{
			Key_Delay++;
			if ((Key_Series == true) && (Key_Delay > KEY_SERIES_DELAY))
			{
				Key_Delay = 0;
				Key_Return = KEY_LIAN | Key_Press; //返回连发的值
				Key_Prev = Key_Press;			   //记住上次的按键
				break;
			}
			if (Key_Delay > KEY_SERIES_FLAG)
			{
				Key_Series = true;
				Key_Delay = 0;
				Key_Return = KEY_LONG | Key_Prev; //返回长按后的值
				break;
			}
		}
	default:
		break;
	}

	return Key_Return;
}
