#ifndef __APP_DATA_H__
#define __APP_DATA_H__

#include "stm32f0xx.h"
#include "rtthread.h"
#include "drv_eeprom.h"
#include "drv_common.h"
#include "drv_hlw8012.h"

#define SYS_PARA_16BIT_NUM      11
#define SYS_PARA_8BIT_NUM       14
#define REF_PARA_16BIT_NUM      22
#define POWER_OUTAGE_16BIT_NUM  60
#define OUTAGE_FLAG_16BIT_NUM   6

#define TOTAL_SYS_PARA_NUM      (SYS_PARA_16BIT_NUM+SYS_PARA_8BIT_NUM)-1

#define TOTOAL_PARA_BYTES_NUM   (SYS_PARA_16BIT_NUM * 2)+SYS_PARA_8BIT_NUM+(REF_PARA_16BIT_NUM * 2)+(POWER_OUTAGE_16BIT_NUM * 2)+(OUTAGE_FLAG_16BIT_NUM*2)

#define SYS_PARA_16BIT_ADDR     1
#define SYS_PARA_8BIT_ADDR      SYS_PARA_16BIT_ADDR + (SYS_PARA_16BIT_NUM * 2)
#define REF_PARA_16BIT_ADDR     SYS_PARA_8BIT_ADDR + SYS_PARA_8BIT_NUM
#define POWER_OUTAGE_PARA_16BIT_ADDR   REF_PARA_16BIT_ADDR + (REF_PARA_16BIT_NUM * 2) 
#define OUTAGE_FLAG_16BIT_ADDR  POWER_OUTAGE_PARA_16BIT_ADDR + (POWER_OUTAGE_16BIT_NUM * 2)

extern void save_sys_parameters(uint8_t x, uint16_t temp);
extern void save_parameters(uint8_t x, uint16_t temp);

#endif


