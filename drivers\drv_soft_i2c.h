#ifndef __DRV_SOFT_I2C_H__
#define __DRV_SOFT_I2C_H__

#include <rtthread.h>
#include "drv_common.h"

#define Pin_SCL_L (GPIOB->BRR = (uint32_t)GPIO_PIN_8)
#define Pin_SCL_H (GPIOB->BSRR = (uint32_t)GPIO_PIN_8)

#define Pin_SDA_L (GPIOB->BRR = (uint32_t)GPIO_PIN_9)
#define Pin_SDA_H (GPIOB->BSRR = (uint32_t)GPIO_PIN_9)

#define Read_SCL_Pin (GPIOB->IDR & GPIO_PIN_8)
#define Read_SDA_Pin (GPIOB->IDR & GPIO_PIN_9)

#define RT_I2C_WR 0x0000
#define RT_I2C_RD (1u << 0)
#define RT_I2C_ADDR_10BIT (1u << 2) /* this is a ten bit chip address */
#define RT_I2C_NO_START (1u << 4)
#define RT_I2C_IGNORE_NACK (1u << 5)
#define RT_I2C_NO_READ_ACK (1u << 6) /* when I2C reading, we do not ACK */
#define RT_I2C_NO_STOP (1u << 7)
struct rt_i2c_msg
{
    uint16_t addr;
    uint16_t flags;
    uint16_t len;
    uint8_t *buf;
};

struct rt_i2c_bus_device;

struct rt_i2c_bus_device_ops
{
    rt_size_t (*master_xfer)(struct rt_i2c_bus_device *bus,
                             struct rt_i2c_msg msgs[],
                             rt_uint32_t num);
    rt_size_t (*slave_xfer)(struct rt_i2c_bus_device *bus,
                            struct rt_i2c_msg msgs[],
                            rt_uint32_t num);
    rt_err_t (*i2c_bus_control)(struct rt_i2c_bus_device *bus,
                                rt_uint32_t,
                                rt_uint32_t);
};

/*for i2c bus driver*/
struct rt_i2c_bus_device
{
    const struct rt_i2c_bus_device_ops *ops;
    rt_uint16_t flags;
    rt_uint16_t addr;
    struct rt_mutex lock;
    rt_uint32_t timeout;
    rt_uint32_t retries;
    void *priv;
};

rt_size_t rt_i2c_transfer(struct rt_i2c_msg msgs[],
                          rt_uint32_t num);
rt_size_t rt_i2c_master_send(rt_uint16_t addr,
                             rt_uint16_t flags,
                             const rt_uint8_t *buf,
                             rt_uint32_t count);
rt_size_t rt_i2c_master_recv(rt_uint16_t addr,
                             rt_uint16_t flags,
                             rt_uint8_t *buf,
                             rt_uint32_t count);

#endif // !__DRV_SOFT_I2C_H__
