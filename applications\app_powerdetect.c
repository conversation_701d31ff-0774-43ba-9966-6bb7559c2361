#include "app_powerdetect.h"
#include "app_process.h"
#include "app_smartcard.h"
#include "app_data.h"
#include "app_voice.h"

#include "drv_gpio.h"
#include "drv_temp.h"
#include "math.h"

struct detect_t
{
	uint8_t stop_recal_flag : 1;
	uint8_t is_detecting_flag : 1;
	uint8_t remote_sucess_flag : 1;
	uint8_t start_flag : 1;
	uint8_t end_flag : 1;
	uint8_t :1;
	uint8_t :1;
	uint8_t :1;
	
	uint8_t charge_time_level : 4;
	uint8_t :4;

	uint8_t check_cnt;
	uint8_t time_cnt;
	uint8_t seg_addr;
	uint8_t seg_cnt;
	uint8_t t_cnt;
	uint8_t detect_power_cnt;

	uint16_t reserveMaxPower;
	uint16_t lastMaxChargePower;
	uint16_t last_remain_energy;
	uint16_t dynamic_total_charge_time;
	uint16_t timeout_cnt;
};
static struct detect_t channel_detect[TOTAL_PORTS_NUM];

struct abnormal_state_t
{
	uint8_t smoke_state_flag : 1;
	uint8_t fire_state_flag : 1;
	uint8_t over_power_flag : 1;
	uint8_t over_temp_flag : 1;
	uint8_t :1;
	uint8_t :1;
	uint8_t :1;
	uint8_t :1;
};
static struct abnormal_state_t abnormal_state;

static uint32_t relay_error_state = 0;
static uint32_t last_relay_error_state = 0;
static uint32_t total_charge_power = 0;
static uint8_t check_power_safe_flag = 0;
static uint8_t temp_symbol = 0, temp_value = 0;

uint8_t get_power_safe_flag(void)
{
	return check_power_safe_flag;
}

uint32_t get_relay_error_state(void)
{
	return relay_error_state;
}

uint32_t get_total_charge_power(void)
{
	return total_charge_power;
}

uint8_t get_charge_time_level(uint8_t x)
{
	return channel_detect[x].charge_time_level;
}

uint8_t get_smoke_alarm_state(void)
{
	return abnormal_state.smoke_state_flag;
}

uint8_t get_fire_alarm_state(void)
{
	return abnormal_state.fire_state_flag;
}

uint8_t get_overpower_alarm_state(void)
{
	return abnormal_state.over_power_flag;
}

uint8_t get_overtemp_alarm_state(void)
{
	return abnormal_state.over_temp_flag;
}

uint8_t get_temp_symbol(void)
{
	return temp_symbol;
}

uint8_t get_temp_value(void)
{
	return temp_value;
}

uint16_t get_dynamic_total_charge_time(uint8_t x)
{
	return channel_detect[x].dynamic_total_charge_time;
}

uint8_t get_start_check_charge_port(uint8_t x)
{
	return channel_detect[x].start_flag;
}

uint8_t get_end_check_charge_port(uint8_t x)
{
	return channel_detect[x].end_flag;
}

void set_detect_state(uint8_t x, uint8_t sta)
{
	channel_detect[x].is_detecting_flag = sta;
}

void set_remote_sucess_flag(uint8_t x, uint8_t sta)
{
	channel_detect[x].remote_sucess_flag = sta;
}
// 设置超时计数器
void set_detect_overtime(uint8_t x, uint8_t temp)
{
	channel_detect[x].time_cnt = temp;
}

// 设置保留的最大功率
void set_detect_reserve_max_power(uint8_t x, uint16_t temp)
{
	channel_detect[x].reserveMaxPower = temp;
}

// 设置上一次保存的最大功率
void set_detect_last_max_power(uint8_t x, uint16_t temp)
{
	channel_detect[x].lastMaxChargePower = temp;
}

void set_start_check_charge_port(uint8_t x, uint8_t sta)
{
	channel_detect[x].start_flag = sta;
}

void set_end_check_charge_port(uint8_t x, uint8_t sta)
{
	channel_detect[x].end_flag = sta;
}

void set_check_charge_port_cnt(uint8_t x, uint8_t temp)
{
	channel_detect[x].seg_cnt = temp;
}

void set_charge_time_level(uint8_t x, uint8_t temp)
{
	channel_detect[x].charge_time_level = temp;
}

void set_power_safe_flag(uint8_t temp)
{
	check_power_safe_flag = temp;
}

void set_stop_recal_flag(uint8_t x, uint8_t temp)
{
	channel_detect[x].stop_recal_flag = temp;
}

void set_dynamic_total_charge_time(uint8_t x, uint16_t temp)
{
	channel_detect[x].dynamic_total_charge_time = temp;
}

void set_detect_power_cnt(uint8_t x, uint8_t temp)
{
	channel_detect[x].detect_power_cnt = temp;
}

static uint8_t check_charge_power_is_safe(uint32_t temp)
{
	t_voice_data voice_data;

	uint8_t x = 0;
	static uint8_t t_disp_delay = 0, overpower_error_count = 0;

	if (temp > (get_limit_total_charge_power() * 100))
	{
		overpower_error_count++;
	}
	if (overpower_error_count >= 15)
	{
		overpower_error_count = 0;
		x = get_last_charge_port();
		if (get_selfstop_state() != 0)
		{
			struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);

			if (get_charge_paytype(x) == LOCAL_SWIPE_TYPE && get_allowrecovery_state() != 0)
			{
				COIN_ENABLE;
				set_local_card_state(0);
				set_online_card_state(0);

				set_back_timer_value(30);

				set_detect_state(x, 0);
				set_error_sta(1);
				set_rechoose_state(1);
				set_stop_disp_flag(1);

				seg_clear();

				set_port_error_state(true, x);
				set_last_charge_port(x);

				set_last_card_amount(get_port_card_last_amount(x));
				set_card_last_serialnum(get_port_card_serilanum(x));

				pdisp->data = get_charge_remain_amount(x);
				if (pdisp->data < 1000)
					pdisp->point = Pos_1;
				else
				{
					pdisp->point = No_Point;
					pdisp->data /= 10;
				}
				pdisp->type = Parameters;
				pdisp->addr = 0;
				pdisp->hide = No_Hide;
				auto_show_digitaltube(pdisp);

				if (get_card_balance() < 1000)
				{
					pdisp->point = Pos_1;
					pdisp->data = get_card_balance();
				}
				else
				{
					pdisp->point = No_Point;
					pdisp->data = get_card_balance() / 10;
				}
				pdisp->type = Parameters;
				pdisp->addr = 1;
				pdisp->hide = No_Hide;
				auto_show_digitaltube(pdisp);

				set_charge_enable_flag(x, 0);
				set_charge_state(x, IDLE_STA);
				set_charge_work_state(x, NoState);
				relay_write(x, GPIO_PIN_RESET);
				init_charge_rom(x, D_NORMAL_MODE);
				reset_filter_data(x);
			}
			else
			{
				pdisp->type = Idle;
				pdisp->addr = x;
				pdisp->data = x + 1;
				pdisp->point = No_Point;
				pdisp->hide = No_Hide;
				auto_show_digitaltube(pdisp);

				if (get_charge_paytype(x) == ONLINE_SWIPE_TYPE)
				{
					set_before_stop_remain_time(get_charge_times(x));
					set_before_stop_remain_battery(get_charge_remain_battery(x));
					set_charge_port_stop_reason(MANUAL_STOP);
					set_upload_all_timer(0);
					send_gprs_mb_cmd(0x16, x);
				}
				stop_charge_port(x);
			}
			rt_free_align(pdisp);
		}
		else
		{
			struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);
			pdisp->type = Idle;
			pdisp->addr = x;
			pdisp->data = x + 1;
			pdisp->point = No_Point;
			pdisp->hide = No_Hide;
			auto_show_digitaltube(pdisp);
			rt_free_align(pdisp);

			if (get_charge_paytype(x) == ONLINE_SWIPE_TYPE)
			{
				set_before_stop_remain_time(get_charge_times(x));
				set_before_stop_remain_battery(get_charge_remain_battery(x));
				set_charge_port_stop_reason(MANUAL_STOP);
				set_upload_all_timer(0);
				send_gprs_mb_cmd(0x16, x);
			}
			stop_charge_port(x);
		}
		//		speeker(Voice_Commd_High_Power);
		voice_data.type = Voice_Commd_High_Power;
		voice_data.power = 0;
		voice_data.time = 0;
		voice_data.balance = 0;
		spell_voice(&voice_data);

		t_disp_delay = 0;
		check_power_safe_flag = 0;

		return RT_ERROR;
	}

	if (get_freeCharge_sta() != 0)
	{
		if (++t_disp_delay >= 25)
		{
			t_disp_delay = 0;
			check_power_safe_flag = 0;
		}
	}
	else
		check_power_safe_flag = 0;

	return RT_EOK;
}

// 动态计算充电时间，以检测到的最大功率作为计算结果
static void find_max_power_value(uint8_t x, uint16_t power)
{
	t_voice_data voice_data;
	uint16_t temp = 0, power_temp = 0;

	power_temp = power;

	if (power_temp > channel_detect[x].lastMaxChargePower)
	{
		if (++channel_detect[x].detect_power_cnt >= 10)
		{
			channel_detect[x].detect_power_cnt = 0;
			if (power_temp > channel_detect[x].lastMaxChargePower)
			{
				channel_detect[x].lastMaxChargePower = power_temp;

				if (channel_detect[x].start_flag != 0)
				{
					channel_detect[x].seg_cnt = 1;
					channel_detect[x].start_flag = 1;
					channel_detect[x].end_flag = 1;
				}

				if (get_last_charge_time(x) != 0)
				{
					temp = get_last_charge_time(x) - (get_float_charge_time() - get_charge_times(x));
					//					set_total_charge_time(x, temp);
					set_charge_times(x, temp);
					//					channel_detect[x].dynamic_total_charge_time = temp;
					channel_detect[x].lastMaxChargePower = channel_detect[x].reserveMaxPower;
					set_last_charge_time(x, 0);
					channel_detect[x].reserveMaxPower = 0;
				}
				else
				{
					if (get_freeCharge_sta() == 0)
					{
						//				if (get_charge_total_time(x) != get_last_total_charge_time(x))
						//				{
						if (power <= get_level_max_power(1))
						{
							temp = get_charge_times(x);
							if(temp >= 1000)
							{
								temp = 999;
							}
							channel_detect[x].dynamic_total_charge_time = get_charge_total_time(x);
						}
						if (power > get_level_max_power(1) && power <= get_level_max_power(2))
						{
							if (channel_detect[x].charge_time_level != 1)
							{
								temp = (get_charge_times(x) * get_level_percentage(2)) / 100;
								if(temp >= 1000)
								{
									temp = 999;
								}
								channel_detect[x].charge_time_level = 1;
								channel_detect[x].dynamic_total_charge_time = temp;
							}
						}
						else if (power > get_level_max_power(2) && power <= get_level_max_power(3))
						{
							if (channel_detect[x].charge_time_level != 2)
							{
								temp = (get_charge_times(x) * get_level_percentage(3)) / 100;
								if(temp >= 1000)
								{
									temp = 999;
								}
								channel_detect[x].charge_time_level = 2;
								channel_detect[x].dynamic_total_charge_time = temp;
							}
						}
						else if (power > get_level_max_power(3) && power <= get_level_max_power(4))
						{
							if (channel_detect[x].charge_time_level != 3)
							{
								temp = (get_charge_times(x) * get_level_percentage(4)) / 100;
								if(temp >= 1000)
								{
									temp = 999;
								}
								channel_detect[x].charge_time_level = 3;
								channel_detect[x].dynamic_total_charge_time = temp;
							}
						}
						if (temp != 0)
						{
							set_charge_times(x, temp);
						}

						if (channel_detect[x].start_flag == 0)
						{
							struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);

							pdisp->type = Parameters;
							pdisp->addr = x;
							pdisp->data = get_charge_times(x);
							pdisp->point = No_Point;
							pdisp->hide = No_Hide;
							auto_show_digitaltube(pdisp);

							rt_free_align(pdisp);
						}
						//				}
					}
				}
			}
		}
	}

	if (channel_detect[x].start_flag != 0 && get_freeCharge_sta() == 0)
	{
		channel_detect[x].end_flag = 1;

		if (get_charge_check_cnt(x) == 0)
			show_circle(x, channel_detect[x].seg_addr);

		if (++channel_detect[x].seg_addr >= 13)
		{
			channel_detect[x].seg_addr = 0;
			if (--channel_detect[x].seg_cnt == 0)
			{
				channel_detect[x].seg_cnt = 0;
				channel_detect[x].start_flag = 0;
				channel_detect[x].end_flag = 0;

				set_charge_minites(x, 0);

				struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);
				pdisp->type = Parameters;
				pdisp->addr = x;
				pdisp->data = get_charge_times(x);
				pdisp->point = No_Point;
				pdisp->hide = No_Hide;
				auto_show_digitaltube(pdisp);
				rt_free_align(pdisp);

				// 充满自停打开，报当前端口功率
				//				if (get_selfstop_state() != 0)
				//				{
				//					spell_voice(SPELL_POWER_TYPE, power_temp);
				//					rt_thread_mdelay(500);
				//				}
				//				//报动态调整后的充电时间
				//				spell_voice(SPELL_TIME_TYPE, get_charge_times(x));
				//				rt_thread_mdelay(500);
				//				speeker(Voice_Commd_Start_Charge);
				voice_data.type = SPELL_POWER_TYPE;
				voice_data.power = power_temp;
				voice_data.time = get_charge_times(x);
				voice_data.balance = 0;
				spell_voice(&voice_data);
			}
		}
	}
}

static void protect_board(void)
{
	uint8_t i;
	
	for (i = 0; i < TOTAL_PORTS_NUM; i++)
	{
		stop_charge_port(i);
		set_charge_state(i, LOCK_STA);
		set_port_lock_state(true, 1 << i);
	}

	save_parameters(OUTAGE_FLAG_16BIT_ADDR + 10, get_port_lock_state());

	struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);
	for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
	{
		main_view(pdisp, i);
	}
	rt_free_align(pdisp);

	COIN_DISABLE;
	set_local_card_state(1);
	set_online_card_state(1);
}

// 当前充电端口功率和消耗电量计算
static void cal_charge_power_entry(void *arg)
{
	t_voice_data voice_data;

	uint32_t temp = 0;
	uint16_t power_value[TOTAL_PORTS_NUM] = {0};
	static uint16_t upload_cnt = 0;
	uint16_t pin_sta = 0;
	uint8_t i = 0, j = 0, k = 0, flag = 0;
	static uint8_t smoke_cnt = 0;
	static uint8_t relay_error_flag = 0;
	static uint8_t check_up_times[TOTAL_PORTS_NUM] = {0};
	static uint8_t check_down_times[TOTAL_PORTS_NUM] = {0};

	// 烟感报警检测
	if (SMOKE_ALARM == GPIO_PIN_RESET)
	{
		if (abnormal_state.smoke_state_flag == 0)
		{
			if (++smoke_cnt > 15)
			{
				smoke_cnt = 0;
				abnormal_state.smoke_state_flag = 1;

				protect_board();
				
				set_upload_all_timer(0);
				send_gprs_mb_cmd(0xB2, 0);
			}
		}
	}
	else if (SMOKE_ALARM != GPIO_PIN_RESET)
	{
		smoke_cnt = 0;
		abnormal_state.smoke_state_flag = 0;
	}

	for (i = 0; i < TOTAL_PORTS_NUM; i++)
	{
		if (get_charge_state(i) != 0 || get_test_state(i) != 0)
		{
			power_value[i] = get_channel_power_value(i);
			flag++;
			temp += power_value[i];
		}
		else
		{
			if (get_channel_power_value(i) > MAX_STICK_POWER_VALUE)
			{
				check_up_times[i]++;
				if (check_up_times[i] >= 5)
				{
					check_up_times[i] = 0;
					relay_error_state |= 1 << i;
				}
			}
			else
			{
				check_down_times[i]++;
				if (check_down_times[i] >= 5)
				{
					check_down_times[i] = 0;
					relay_error_state &= ~(1 << i);
				}
			}
		}
	}

	if (relay_error_state != 0 && relay_error_flag == 0 && relay_error_state != last_relay_error_state)
	{
		relay_error_flag = 1;
		last_relay_error_state = relay_error_state;
		send_gprs_mb_cmd(0xB2, 0);
	}
	else if (relay_error_state == 0)
	{
		relay_error_flag = 0;
		last_relay_error_state = relay_error_state;
	}

	if (flag == 0)
		total_charge_power = 0;
	else
	{
		total_charge_power = temp;
		if (check_charge_power_is_safe(total_charge_power) == RT_ERROR)
			return;
	}

	// 检测功率
	for (i = 0; i < TOTAL_PORTS_NUM; i++)
	{
		if (get_charge_state(i) != 0 || get_test_state(i) != 0 || get_calibrate_state(i) != 0)
		{
			set_charge_power_value(i, power_value[i]);
		}
		// 端口支持充满自停功能才能进行动态时间调整和检测电量，以及检测是否有负载连接和过载的情况
		if (get_charge_state(i) != 0 && get_error_highpower_state(i) == 0)
		{
			// 滤除刚开始检测到的0功率值
			if (power_value[i] < MIN_POWER_VALUE)
			{
				// 5分钟以内,出现检测不到功率,可以重复检测三次,充电超过5分钟,出现检测不到功率,直接关掉充电端口
				// 充满自停打开才能对无功率端口进行报错
				if (get_selfstop_state() != 0)
				{
					if (get_floatcharge_state(i) != 0)
					{
						if (get_last_charge_time(i) > get_float_charge_time())
						{
							temp = get_last_charge_time(i) - (get_float_charge_time() - get_charge_times(i));
						}
						else
						{
							temp = get_charge_times(i);
						}
						temp = channel_detect[i].dynamic_total_charge_time - temp;
					}
					else
					{
						temp = channel_detect[i].dynamic_total_charge_time - get_charge_times(i);
					}
					if (temp <= 5)
					{
						// 付费模式下,低于一定功率,先检测5s,如果还是低功率,就报错或者关断
						if (++channel_detect[i].time_cnt >= 5)
						{
							channel_detect[i].time_cnt = 0;
							if(++channel_detect[i].timeout_cnt >= get_detect_timeout_time())
							{
								channel_detect[i].timeout_cnt = 0;
								channel_detect[i].end_flag = 0;
								channel_detect[i].check_cnt = 0;

								set_charge_state(i, ERROR_STA);
								set_last_charge_port(i);

								set_last_card_amount(get_port_card_last_amount(i));
								set_last_charge_amount(get_charge_remain_amount(i));

								set_port_error_state(true, i);

								if (get_charge_recheck_state(i) != 0)
								{
									set_error_cnt(i, 0);
									set_charge_recheck_state(i, 0);
								}

								if (get_charge_noconnect_state(i) == 0)
								{
									set_charge_noconnect_state(i, 1);
									set_charge_reconnect_state(i, 1);

									//								speeker(Voice_Commd_Abnomal_Charge);
									voice_data.type = Voice_Commd_Abnomal_Charge;
									voice_data.power = 0;
									voice_data.time = 0;
									voice_data.balance = 0;
									spell_voice(&voice_data);
								}

								COIN_DISABLE;
								if (get_charge_paytype(i) == COIN_TYPE)
								{
									set_online_card_state(1);
									set_local_card_state(1);
									set_last_paytype(COIN_TYPE);
								}
								else if (get_charge_paytype(i) == LOCAL_SWIPE_TYPE)
								{
									set_online_card_state(1);
									if (get_allowrecovery_state() != 0)
									{
										set_local_card_state(0);
										set_card_last_serialnum(get_port_card_serilanum(i));
									}
									else
									{
										set_local_card_state(1);
									}
									set_last_paytype(LOCAL_SWIPE_TYPE);
								}
								else if (get_charge_paytype(i) == ONLINE_SWIPE_TYPE)
								{
									set_online_card_state(1);
									set_local_card_state(1);
									set_last_paytype(ONLINE_SWIPE_TYPE);
								}
								else if (get_charge_paytype(i) == REMOTE_TYPE)
								{
									set_online_card_state(1);
									set_local_card_state(1);
									set_last_paytype(REMOTE_TYPE);
								}
							}
						}
					}
					else
					{
						// 付费模式下,低于一定功率,先检测5s,如果还是低功率,就报错或者关断
						if (++channel_detect[i].time_cnt >= 5)
						{
							channel_detect[i].time_cnt = 0;
							if (++channel_detect[i].timeout_cnt >= get_detect_timeout_time())
							{
								channel_detect[i].timeout_cnt = 0;
								channel_detect[i].end_flag = 0;

								struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);

								if (get_charge_paytype(i) == LOCAL_SWIPE_TYPE && get_allowrecovery_state() != 0)
								{
									COIN_ENABLE;
									set_local_card_state(0);
									set_online_card_state(0);

									set_back_timer_value(10);

									// set_detect_state(i, 0);
									// set_error_sta(1);
									// set_rechoose_state(1);
									set_stop_disp_flag(1);

									// set_port_error_state(true, i);
									// set_last_charge_port(i);

									// set_last_card_amount(get_port_card_last_amount(i));
									// set_card_last_serialnum(get_port_card_serilanum(i));

									set_card_balance(get_port_card_last_amount(i));

									seg_clear();
									pdisp->data = get_charge_remain_amount(i);
									if (pdisp->data < 1000)
										pdisp->point = Pos_1;
									else
									{
										pdisp->point = No_Point;
										pdisp->data /= 10;
									}
									pdisp->type = Parameters;
									pdisp->addr = 0;
									pdisp->hide = No_Hide;
									auto_show_digitaltube(pdisp);

									if (get_card_balance() < 1000)
									{
										pdisp->point = Pos_1;
										pdisp->data = get_card_balance();
									}
									else
									{
										pdisp->point = No_Point;
										pdisp->data = get_card_balance() / 10;
									}
									pdisp->type = Parameters;
									pdisp->addr = 1;
									pdisp->hide = No_Hide;
									auto_show_digitaltube(pdisp);

									set_charge_enable_flag(i, 0);
									set_charge_state(i, IDLE_STA);
									set_charge_work_state(i, NoState);

									relay_write(i, GPIO_PIN_RESET);
									init_charge_rom(i, D_NORMAL_MODE);
									reset_filter_data(i);

									set_manual_recovery_amount_flag(i, 1);

									//								speeker(Voice_Commd_Card_Refund);
									voice_data.type = Voice_Commd_Card_Refund;
									voice_data.power = 0;
									voice_data.time = 0;
									voice_data.balance = 0;
									spell_voice(&voice_data);
								}
								else
								{
									pdisp->type = Idle;
									pdisp->addr = i;
									pdisp->data = i + 1;
									pdisp->point = No_Point;
									pdisp->hide = No_Hide;
									auto_show_digitaltube(pdisp);

									if (get_charge_paytype(i) == ONLINE_SWIPE_TYPE || get_charge_paytype(i) == REMOTE_TYPE)
									{
										if (get_floatcharge_state(i) != 0)
										{
											if (get_last_charge_time(i) > get_float_charge_time())
											{
												set_before_stop_remain_time(get_last_charge_time(i) - (get_float_charge_time() - get_charge_times(i)));
											}
											else
											{
												set_before_stop_remain_time(get_charge_times(i));
											}
										}
										else
										{
											set_before_stop_remain_time(get_charge_times(i));
										}
										set_before_stop_remain_battery(get_charge_remain_battery(i));
										set_charge_port_stop_reason(MANUAL_STOP);
										set_upload_all_timer(0);
										send_gprs_mb_cmd(0x16, i);
									}
									stop_charge_port(i);
								}
								rt_free_align(pdisp);
							}
						}
					}
				}
			}
			else
			{
				// 当前功率超过第四档设定的最大功率则马上语音报错，关闭继电器，不清零充电参数
				if (power_value[i] > get_limited_power_value())
				{
					// 付费模式下,充满自停打开的情况下才能对超功率报错
					if (get_selfstop_state() != 0)
					{
						channel_detect[i].end_flag = 0;
						check_power_safe_flag = 0;

						if (get_freeCharge_sta() == 0)
						{
							relay_write(i, GPIO_PIN_RESET);

							init_charge_rom(i, D_NORMAL_MODE);
							reset_filter_data(i);

//							if (get_charge_reconnect_state(i) != 0)
//							{
//								set_error_cnt(i, 0);
//								set_charge_noconnect_state(i, 0);
//								set_charge_reconnect_state(i, 0);
//							}

//							set_error_highpower_state(i, 1);
//							set_charge_recheck_state(i, 1);
//							set_charge_state(i, HIGH_STA);

//							COIN_DISABLE;

//							if (get_charge_paytype(i) == COIN_TYPE || get_charge_paytype(i) == REMOTE_TYPE)
//							{
//								set_local_card_state(1);
//								set_online_card_state(1);
//							}
							// 检查是否支持余额回收功能
//							if (get_allowrecovery_state() == 0)
//							{
//								set_local_card_state(1);
//							}

							channel_detect[i].check_cnt = 0;
						}
						else
						{
							struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);
							pdisp->type = Idle;
							pdisp->addr = i;
							pdisp->data = i + 1;
							pdisp->point = No_Point;
							pdisp->hide = No_Hide;
							auto_show_digitaltube(pdisp);
							rt_free_align(pdisp);

							stop_charge_port(i);
						}

						voice_data.type = Voice_Commd_High_Power;
						voice_data.power = 0;
						voice_data.time = 0;
						voice_data.balance = 0;
						spell_voice(&voice_data);
					}
				}
				else if (power_value[i] > MIN_POWER_VALUE && power_value[i] <= get_limited_power_value())
				{
					// 付费模式下,充满自停打开的情况下才能处理支付方式的开关
					if (get_freeCharge_sta() == 0 && get_selfstop_state() != 0)
					{
						for (j = 0; j < TOTAL_PORTS_NUM; j++)
						{
							if (channel_detect[j].is_detecting_flag != 0 && get_charge_paytype(j) != NO_TYPE)
							{
								k++;
								if (get_charge_paytype(j) == COIN_TYPE || get_charge_paytype(j) == ONLINE_SWIPE_TYPE)
									set_local_card_state(1);
								else if (get_charge_paytype(j) == LOCAL_SWIPE_TYPE)
								{
									if (get_allowrecovery_state() != 0)
									{
										if (get_charge_state(j) == ERROR_STA)
											set_local_card_state(0);
									}
									else
									{
										set_local_card_state(1);
									}
								}
							}
						}
						if (k != 0)
						{
							COIN_DISABLE;
							set_online_card_state(1);
						}
						else
						{
							COIN_ENABLE;
							set_online_card_state(0);
							if (get_local_swipe_state() == 0)
								set_local_card_state(0);
						}
					}

					channel_detect[i].time_cnt = 0;
					channel_detect[i].timeout_cnt = 0;
					channel_detect[i].is_detecting_flag = 0;

					set_error_cnt(i, 0);
					set_port_error_state(false, i);
					set_charge_work_state(i, NomalChargeState);
					set_charge_state(i, NOMAL_STA);
					if (get_selfstop_state() != 0)
					{
						set_charge_reconnect_state(i, 0);
						set_charge_recheck_state(i, 0);
					}
					set_charge_noconnect_state(i, 0);
					set_error_highpower_state(i, 0);

					// 充满自停打开的情况下,根据功率浮动，自动调整充电时间
					if (get_floatcharge_state(i) == 0 && get_selfstop_state() != 0 && channel_detect[i].stop_recal_flag == 0 && get_floatcharge_state(i) == 0)
					{
						find_max_power_value(i, power_value[i]);
					}

					// 付费模式下才能计算电量使用的情况
					if (get_charge_total_energies(i) != 0 && get_freeCharge_sta() == 0 && get_selfstop_state() != 0)
					{
						// 计算当前充电口的剩余电量，只有设定功率值与消耗的功率不相等时才进行计算
						// 计算得到当前充电口的功率和电量
						temp = get_charge_total_energies(i) - get_channel_energy_value(i);
						if (channel_detect[i].last_remain_energy != temp)
						{
							set_charge_remain_battery(i, temp);
							channel_detect[i].last_remain_energy = temp;
						}

						// 充电功率达到设定功率则关闭端口充电
						if (get_charge_remain_battery(i) == 0)
						{
							struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);

							pdisp->type = Idle;
							pdisp->addr = i;
							pdisp->data = i + 1;
							pdisp->point = No_Point;
							pdisp->hide = No_Hide;
							auto_show_digitaltube(pdisp);

							rt_free_align(pdisp);

							if (get_charge_remain_battery(i) == 0)
							{
								set_before_stop_remain_time(get_charge_times(i));
								set_before_stop_remain_battery(get_charge_remain_battery(i));
								if (get_floatcharge_state(i) != 0)
									set_charge_port_stop_reason(AUTO_STOP);
								else
									set_charge_port_stop_reason(POWER_ENERGY_DONE_STOP);

								// 对当前端口的所有充电状态和参数清零
								// 电量消耗关断的优先级大于充电时间关断，电量先消耗完则立马关断当前充电端口
								stop_charge_port(i);

								set_upload_all_timer(0);
								send_gprs_mb_cmd(0x16, i);
							}
						}
					}

					// 充满自停打开的情况下才能检测当前负载功率是否到了设置的最大浮充功率，到了最大浮充功率值，重置当前充电时间为浮充时间
					if (get_selfstop_state() != 0 && channel_detect[i].start_flag == 0)
					{
						if ((power_value[i] <= get_max_float_charge_value()) && (power_value[i] > MIN_POWER_VALUE))
						{
							// 达到浮充功率范围，等待功率稳定下来，有一段等待时间
							if (++channel_detect[i].check_cnt >= 50 && get_floatcharge_state(i) == 0)
							{
								channel_detect[i].check_cnt = 0;
								if ((power_value[i] <= get_max_float_charge_value()) && (power_value[i] > MIN_POWER_VALUE))
								{
									// 充满自停打开，浮充时间不为0，当负载功率低于最大浮充功率，则进入浮充状态，关断依据时间和功率两个参数谁先消耗完就关断
									// 充满自停关闭，不管有没有负载连接，直到充电时间走完为止,不再检测电量
									if (get_float_charge_time() != 0)
									{
										set_floatcharge_state(i, 1);

										channel_detect[i].reserveMaxPower = channel_detect[i].lastMaxChargePower;
										channel_detect[i].lastMaxChargePower = 0;
										set_last_charge_time(i, get_charge_times(i));
										// 当前充电时间大于浮充时间，则把充电时间设置为浮充时间，当前充电时间小于浮充时间，则继续以当前剩余的充电时间计时
										if (get_charge_times(i) > get_float_charge_time())
										{
											set_charge_minites(i, 0);
											set_charge_times(i, get_float_charge_time());
										}
									}
								}
							}
						}
						else
						{
							channel_detect[i].check_cnt = 0;
							set_floatcharge_state(i, 0);
						}
					}
				}
			}
		}

		else if (get_test_state(i) != 0)
		{
			if (power_value[i] > MIN_POWER_VALUE)
			{
				// 每次功率检测结束都需要重新初始化参数
				relay_write(i, GPIO_PIN_RESET);

				init_charge_rom(i, D_NORMAL_MODE);
				reset_filter_data(i);

				set_test_times(0);
				set_test_state(i, 0);
				set_test_connected_state(i, 1);

				set_charge_state(i, IDLE_STA);

				//				speeker(Voice_Commd_Charge_Connected);
				voice_data.type = Voice_Commd_Charge_Connected;
				voice_data.power = 0;
				voice_data.time = 0;
				voice_data.balance = 0;
				spell_voice(&voice_data);
			}
		}
	}

	// adc采样
	get_adc_value();
	upload_cnt++;
	switch (upload_cnt)
	{
	// 检测本地温度,过温则上传报警信号
	case 150:
		upload_cnt = 0;

		int temp = get_temp();

		if (temp >= 0)
			temp_symbol = 0;
		else
			temp_symbol = 1;

		temp_value = (uint8_t)ABS(temp);

		if (temp > get_limited_temp() && abnormal_state.over_temp_flag == 0)
		{
			abnormal_state.over_temp_flag = 1;
			
			protect_board();

			set_upload_all_timer(0);
			send_gprs_mb_cmd(0xB2, 0);
		}
		else
			abnormal_state.over_temp_flag = 0;
		break;
	default:
		break;
	}
}

// 创建定时器处理动态线程控制块
#define TIMER_THREAD_STACK_SIZE 600
static rt_thread_t dynamic_detect = RT_NULL;
void create_detect_dynamic_thread(void)
{
	/* 创建线程 1，名称是 thread1，入口是 thread1_entry*/
	dynamic_detect = rt_thread_create("d_detect",
									  cal_charge_power_entry,
									  RT_NULL,
									  TIMER_THREAD_STACK_SIZE,
									  2,
									  5);

	/* 如果获得线程控制块，启动这个线程 */
	if (dynamic_detect != RT_NULL)
	{
		//        rt_kprintf("create transmit dynamic thread sucess \r\n");
		rt_thread_startup(dynamic_detect);
	}
}

static void detect_entry(void *arg)
{
	while (1)
	{
		if (rt_sem_take(&detect_sem, RT_WAITING_FOREVER) == RT_EOK)
		{
			create_detect_dynamic_thread();
		}
	}
}

#define DETECT_THREAD_PRIORITY 6
#define DETECT_THREAD_TIMESLICE 1

ALIGN(RT_ALIGN_SIZE)
static char detect_stack[250];
static struct rt_thread detect;
int detect_thread(void)
{
	rt_thread_init(&detect,
				   "detect",
				   detect_entry,
				   RT_NULL,
				   &detect_stack[0],
				   sizeof(detect_stack),
				   DETECT_THREAD_PRIORITY,
				   1);
	rt_thread_startup(&detect);

	return RT_EOK;
}
INIT_APP_EXPORT(detect_thread);
