#include "drv_eeprom.h"
#include "drv_common.h"

#define AT24C02_ADDR_WRITE  0xA0
#define AT24C02_ADDR_READ   0xA1
#define AT24CXX_PAGE_SIZE	8

#define I2C_TIMEOUT  10 /*<! Value of Timeout when I2C communication fails */

static I2C_HandleTypeDef hi2c1;

extern void delay_ms(uint16_t ms);

/* I2C1 init function */
int I2C1_Init(void)
{
    hi2c1.Instance = I2C1;
    hi2c1.Init.Timing = 0x2000090E;
    hi2c1.Init.OwnAddress1 = 0;
    hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
    hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
    hi2c1.Init.OwnAddress2 = 0;
    hi2c1.Init.OwnAddress2Masks = I2C_OA2_NOMASK;
    hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
    hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
    
    if (HAL_I2C_Init(&hi2c1) != HAL_OK)
    {
        _Error_Handler(__FILE__, __LINE__);
    }
    /** Configure Analogue filter
    */
    if (HAL_I2CEx_ConfigAnalogFilter(&hi2c1, I2C_ANALOGFILTER_ENABLE) != HAL_OK)
    {
        _Error_Handler(__FILE__, __LINE__);
    }
    /** Configure Digital filter
    */
    if (HAL_I2CEx_ConfigDigitalFilter(&hi2c1, 0) != HAL_OK)
    {
        _Error_Handler(__FILE__, __LINE__);
    }
    
    return RT_EOK;
}
//INIT_BOARD_EXPORT(I2C1_Init);

void HAL_I2C_MspInit(I2C_HandleTypeDef* i2cHandle)
{

    GPIO_InitTypeDef GPIO_InitStruct = {0};

    if(i2cHandle->Instance == I2C1)
    {
        /* I2C1 clock enable */
        __HAL_RCC_I2C1_CLK_ENABLE();
        __HAL_RCC_GPIOB_CLK_ENABLE();
        /**I2C1 GPIO Configuration
        PB8     ------> I2C1_SCL
        PB9     ------> I2C1_SDA
        */
        GPIO_InitStruct.Pin = GPIO_PIN_8 | GPIO_PIN_9;
        GPIO_InitStruct.Mode = GPIO_MODE_AF_OD;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
        GPIO_InitStruct.Alternate = GPIO_AF1_I2C1;
        HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
        
        HAL_I2CEx_EnableFastModePlus(SYSCFG_CFGR1_I2C_FMP_PB8);
        HAL_I2CEx_EnableFastModePlus(SYSCFG_CFGR1_I2C_FMP_PB9);

        /* I2C1 interrupt Init */
        HAL_NVIC_SetPriority(I2C1_IRQn, 1, 0);
        HAL_NVIC_EnableIRQ(I2C1_IRQn);
    }
}

void HAL_I2C_MspDeInit(I2C_HandleTypeDef* i2cHandle)
{

    if(i2cHandle->Instance == I2C1)
    {
        /* Peripheral clock disable */
        __HAL_RCC_I2C1_CLK_DISABLE();

        /**I2C1 GPIO Configuration
        PB8     ------> I2C1_SCL
        PB9     ------> I2C1_SDA
        */
        HAL_GPIO_DeInit(GPIOB, GPIO_PIN_8);

        HAL_GPIO_DeInit(GPIOB, GPIO_PIN_9);

        /* I2C1 interrupt Deinit */
        HAL_NVIC_DisableIRQ(I2C1_IRQn);
    }
}

/**
  * @brief  Manages error callback by re-initializing I2C.
  * @param  Addr: I2C Address
  */
static void I2Cx_Error(uint8_t Addr)
{
    /* De-initialize the IOE comunication BUS */
    HAL_I2C_DeInit(&hi2c1);

    /* Re-Initiaize the IOE comunication BUS */
    I2C1_Init();  
}

/**
 * @brief       AT24C02任意地址写一个字节数据
 * @param       addr —— 写数据的地址（0-255）
 * @param       dat  —— 存放写入数据的地址
 * @retval      成功 —— HAL_OK
*/
HAL_StatusTypeDef At24c02_Write_Byte(uint16_t addr, uint8_t* dat)
{
    HAL_StatusTypeDef status = HAL_OK;
    
    status = HAL_I2C_Mem_Write_IT(&hi2c1, AT24C02_ADDR_WRITE, addr, I2C_MEMADD_SIZE_8BIT, dat, 1);
//    status = HAL_I2C_Mem_Write(&hi2c1, AT24C02_ADDR_WRITE, addr, I2C_MEMADD_SIZE_8BIT, dat, 1, I2C_TIMEOUT);
	
    /* Check the communication status */
    if(status != HAL_OK)
    {
        /* I2C error occured */
        I2Cx_Error(AT24C02_ADDR_WRITE);
    }  
	
	return status;
}

HAL_StatusTypeDef AT24CXX_PageWrite(uint16_t MemAddress, uint8_t* pBuffer, uint32_t BufferSize)
{
	HAL_StatusTypeDef status = HAL_OK;
	
	status = HAL_I2C_Mem_Write_IT(&hi2c1, AT24C02_ADDR_WRITE, MemAddress, I2C_MEMADD_SIZE_8BIT, pBuffer, BufferSize);
//    status = HAL_I2C_Mem_Write(&hi2c1, AT24C02_ADDR_WRITE, MemAddress, I2C_MEMADD_SIZE_8BIT, pBuffer, BufferSize, I2C_TIMEOUT);
    
    /* Check the communication status */
    if(status != HAL_OK)
    {
        /* I2C error occured */
        I2Cx_Error(AT24C02_ADDR_WRITE);
    }  
	
	return status;
}

/**
 * @brief       AT24C02任意地址连续写多个字节数据
 * @param       addr —— 写数据的地址（0-255）
 * @param       dat  —— 存放写入数据的地址
 * @retval      成功 —— HAL_OK
*/
HAL_StatusTypeDef At24c02_Write_NByte(uint16_t addr, uint8_t* dat, uint16_t size)
{
	uint8_t NumOfPage = 0, NumOfSingle = 0, Addr = 0, count = 0;
	HAL_StatusTypeDef err = HAL_OK;

	Addr = addr % AT24CXX_PAGE_SIZE;
	count = AT24CXX_PAGE_SIZE - Addr;
	NumOfPage =  size / AT24CXX_PAGE_SIZE;
	NumOfSingle = size % AT24CXX_PAGE_SIZE;
 
	/* If WriteAddr is I2C_PageSize aligned  */
	if(Addr == 0) 
	{
		/* If NumByteToWrite < I2C_PageSize */
		if(NumOfPage == 0) 
		{
			return (AT24CXX_PageWrite(addr,dat,size));
		}
		/* If NumByteToWrite > I2C_PageSize */
		else  
		{
			while(NumOfPage --)
			{
				err = AT24CXX_PageWrite(addr, dat, AT24CXX_PAGE_SIZE);
				if(err != HAL_OK)
					return err; 
				addr +=  AT24CXX_PAGE_SIZE;
				dat += AT24CXX_PAGE_SIZE;
			}

			if(NumOfSingle != 0)
			{
				return (AT24CXX_PageWrite(addr, dat, NumOfSingle)); 
			}
		}
	}
	  /* If WriteAddr is not I2C_PageSize aligned  */
	else 
	{
		/* If NumByteToWrite < I2C_PageSize */
		if(size <= count) 
		{
			return (AT24CXX_PageWrite(addr, dat, NumOfSingle));
		}
		/* If NumByteToWrite > I2C_PageSize */
		else
		{
			size -= count;
			NumOfPage =  size / AT24CXX_PAGE_SIZE;
			NumOfSingle = size % AT24CXX_PAGE_SIZE;	
		  
			err = AT24CXX_PageWrite(addr, dat, count);
			if(err != HAL_OK)
				return err; 
			addr += count;
			dat += count;
		  
			while(NumOfPage --)
			{
				err = AT24CXX_PageWrite(addr, dat, AT24CXX_PAGE_SIZE);
				if(err != HAL_OK)
					return err;
				addr +=  AT24CXX_PAGE_SIZE;
				dat += AT24CXX_PAGE_SIZE;  
			}
			
			if(NumOfSingle != 0)
			{
				return (AT24CXX_PageWrite(addr, dat, NumOfSingle)); 
			}
		}
	}
    
    return err;
}
/**
  * @brief  Camera reads single data.
  * @param  Reg: Reg address 
  * @retval Read data
  */
HAL_StatusTypeDef AT24CXX_ReadByte(uint8_t addr, uint8_t Value)
{
    HAL_StatusTypeDef status = HAL_OK;

    status = HAL_I2C_Mem_Read(&hi2c1, AT24C02_ADDR_READ, addr, I2C_MEMADD_SIZE_8BIT, &Value, 1, I2C_TIMEOUT);

    /* Check the communication status */
    if(status != HAL_OK)
    {
        /* Execute user timeout callback */
        I2Cx_Error(AT24C02_ADDR_READ);
    }

    return status;   
}

/**
 * @brief       AT24C02任意地址连续读多个字节数据
 * @param       addr —— 读数据的地址（0-255）
 * @param       dat  —— 存放读出数据的地址
 * @retval      成功 —— HAL_OK
*/
HAL_StatusTypeDef At24c02_Read_NByte(uint16_t addr, uint8_t* recv_buf, uint16_t size)
{
    HAL_StatusTypeDef err = HAL_OK;
    
    err = HAL_I2C_Mem_Read_IT(&hi2c1, AT24C02_ADDR_READ, addr, I2C_MEMADD_SIZE_8BIT, recv_buf, size);
//    err = HAL_I2C_Mem_Read(&hi2c1, AT24C02_ADDR_READ, addr, I2C_MEMADD_SIZE_8BIT, recv_buf, size, I2C_TIMEOUT);
    
    /* Check the communication status */
    if(err != HAL_OK)
    {
        /* I2C error occured */
        I2Cx_Error(AT24C02_ADDR_READ);
    }
    
    return err;
}

void I2C1_IRQHandler(void)
{
    rt_interrupt_enter();
    
    if (hi2c1.Instance->ISR & (I2C_FLAG_BERR | I2C_FLAG_ARLO | I2C_FLAG_OVR)) 
    {
        HAL_I2C_ER_IRQHandler(&hi2c1);
    } 
    else 
    {
        HAL_I2C_EV_IRQHandler(&hi2c1);
    }
    
    rt_interrupt_leave();
}

