#include "drv_buzzer.h"

#define BUZZER_DELAY 400

/*---------------------------------  0  <USER>  <GROUP>  3  4  5  6  7  8  9 ----------*/
static const uint8_t voice_tab[] = {66, 67, 68, 69, 70, 71, 72, 73, 74, 75};

void speeker(speekType cmd)
{
//    rt_mutex_t buzzer_lock;

//    buzzer_lock = rt_mutex_create("mutex_buzzer", RT_IPC_FLAG_FIFO);
//    if (buzzer_lock == RT_NULL)
//    {
//        return;
//    }

//    rt_mutex_take(buzzer_lock, RT_WAITING_FOREVER);

    BUZZER_RST_HIGH;
    rt_hw_us_delay(200);
    BUZZER_RST_LOW;

    while (cmd > 0)
    {
        BUZZER_CNT_HIGH;
        rt_hw_us_delay(100);
        BUZZER_CNT_LOW;
        rt_hw_us_delay(100);
        cmd--;
    }

    //	rt_thread_mdelay(100);

//    rt_mutex_release(buzzer_lock);

//    rt_mutex_delete(buzzer_lock);
}

void spell_balance(uint16_t balance)
{
    uint8_t temp[4] = {0};

    temp[0] = balance % 10000 / 1000;
    temp[1] = balance % 1000 / 100;
    temp[2] = balance % 100 / 10;
    temp[3] = balance % 10;

    if (temp[0] != 0)
    {
        //百
        speeker(voice_tab[temp[0]]);
        rt_thread_mdelay(BUZZER_DELAY);
        speeker(Voice_Commd_Hundred);
        rt_thread_mdelay(BUZZER_DELAY);
    }

    if (temp[0] != 0)
    {
        if (temp[1] != 0)
        {
            //十
            speeker(voice_tab[temp[1]]);
            rt_thread_mdelay(BUZZER_DELAY);
            speeker(Voice_Commd_Ten);
            rt_thread_mdelay(BUZZER_DELAY);
        }
        if (temp[1] == 0 && temp[2] != 0)
        {
            speeker(voice_tab[temp[1]]);
            rt_thread_mdelay(BUZZER_DELAY);
        }
        if (temp[2] != 0)
        {
            //个
            speeker(voice_tab[temp[2]]);
            rt_thread_mdelay(BUZZER_DELAY);
        }
    }
    else
    {
        if (temp[1] != 0)
        {
            //十
            speeker(voice_tab[temp[1]]);
            rt_thread_mdelay(BUZZER_DELAY);
            speeker(Voice_Commd_Ten);
            rt_thread_mdelay(BUZZER_DELAY);
            if (temp[2] != 0)
            {
                //个
                speeker(voice_tab[temp[2]]);
                rt_thread_mdelay(BUZZER_DELAY);
            }
        }
        else
        {
            //个
            speeker(voice_tab[temp[2]]);
            rt_thread_mdelay(BUZZER_DELAY);
        }
    }

    //点
    speeker(Voice_Commd_Point);
    rt_thread_mdelay(BUZZER_DELAY);

    //角
    speeker(voice_tab[temp[3]]);
    rt_thread_mdelay(BUZZER_DELAY);

    //元
    speeker(Voice_Commd_Yuan);
}

void spell_current_data(uint8_t type, uint16_t data)
{
    uint8_t temp[4] = {0};

    temp[0] = data % 10000 / 1000;
    temp[1] = data % 1000 / 100;
    temp[2] = data % 100 / 10;
    temp[3] = data % 10;

    if (type == SPELL_POWER_TYPE)
    {
        speeker(Voice_Commd_Current_Charge_Power);
        rt_thread_mdelay(1500);
    }
    else if (SPELL_TIME_TYPE)
    {
        speeker(Voice_Commd_Current_Charge_Time);
        rt_thread_mdelay(1500);
    }

    if (temp[0] != 0)
    {
        //千
        speeker(voice_tab[temp[0]]);
        rt_thread_mdelay(BUZZER_DELAY);
        speeker(Voice_Commd_Thousand);
        rt_thread_mdelay(BUZZER_DELAY);
    }

    if (temp[0] != 0)
    {
        if (temp[1] != 0)
        {
            //百
            speeker(voice_tab[temp[1]]);
            rt_thread_mdelay(BUZZER_DELAY);
            speeker(Voice_Commd_Hundred);
            rt_thread_mdelay(BUZZER_DELAY);
        }
        else if (temp[1] == 0 && (temp[2] != 0 || temp[3] != 0))
        {
            //百
            speeker(voice_tab[temp[1]]);
            rt_thread_mdelay(BUZZER_DELAY);
        }
        if (temp[2] != 0)
        {
            //十
            speeker(voice_tab[temp[2]]);
            rt_thread_mdelay(BUZZER_DELAY);
            speeker(Voice_Commd_Ten);
            rt_thread_mdelay(BUZZER_DELAY);
        }
        else if ((temp[1] != 0 || temp[2] != 0) && temp[3] != 0)
        {
            //十
            speeker(voice_tab[temp[2]]);
            rt_thread_mdelay(BUZZER_DELAY);
        }
        if (temp[3] != 0)
        {
            //个
            speeker(voice_tab[temp[3]]);
            rt_thread_mdelay(BUZZER_DELAY);
        }
    }
    else
    {
        if (temp[1] != 0)
        {
            //百
            speeker(voice_tab[temp[1]]);
            rt_thread_mdelay(BUZZER_DELAY);
            speeker(Voice_Commd_Hundred);
            rt_thread_mdelay(BUZZER_DELAY);
        }

        if (temp[1] != 0)
        {
            if (temp[2] != 0)
            {
                //十
                speeker(voice_tab[temp[2]]);
                rt_thread_mdelay(BUZZER_DELAY);
                speeker(Voice_Commd_Ten);
                rt_thread_mdelay(BUZZER_DELAY);
            }
            else if (temp[2] == 0 && temp[3] != 0)
            {
                //十
                speeker(voice_tab[temp[2]]);
                rt_thread_mdelay(BUZZER_DELAY);
            }
            if (temp[3] != 0)
            {
                //个
                speeker(voice_tab[temp[3]]);
                rt_thread_mdelay(BUZZER_DELAY);
            }
        }
        else
        {
            if (temp[2] != 0)
            {
                //十
                speeker(voice_tab[temp[2]]);
                rt_thread_mdelay(BUZZER_DELAY);
                speeker(Voice_Commd_Ten);
                rt_thread_mdelay(BUZZER_DELAY);
                if (temp[3] != 0)
                {
                    //个
                    speeker(voice_tab[temp[3]]);
                    rt_thread_mdelay(BUZZER_DELAY);
                }
            }
            else
            {
                //个
                speeker(voice_tab[temp[3]]);
                rt_thread_mdelay(BUZZER_DELAY);
            }
        }
    }
    if (type == SPELL_POWER_TYPE)
    {
        //瓦
        speeker(Voice_Commd_Watt);
    }
    else if (type == SPELL_TIME_TYPE)
    {
        //分钟
        speeker(Voice_Commd_Minutes);
    }
}
