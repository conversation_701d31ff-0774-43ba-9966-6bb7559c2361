#include "drv_relay.h"

#define STM32_PIN(index, gpio, gpio_index)       \
    {                                            \
        index, GPIO##gpio, GPIO_PIN_##gpio_index \
    }

#define ITEM_NUM(items) sizeof(items) / sizeof(items[0])

struct pin_index
{
    int index;
    GPIO_TypeDef *gpio;
    uint32_t pin;
};

static const struct pin_index pins[] = {
    //    STM32_PIN(0, A, 8),
    //    STM32_PIN(1, A, 9),
    //    STM32_PIN(2, A, 10),
    //    STM32_PIN(3, A, 11),
    //    STM32_PIN(4, A, 12),
    //    STM32_PIN(5, A, 15),
    STM32_PIN(0, A, 1),
    STM32_PIN(1, A, 4),
    STM32_PIN(2, A, 5),
    STM32_PIN(3, A, 6),
    STM32_PIN(4, A, 7),
    STM32_PIN(5, C, 4),
    STM32_PIN(6, C, 5),
    STM32_PIN(7, B, 0),
    STM32_PIN(8, B, 1),
    STM32_PIN(9, B, 2)};

static const struct pin_index *get_pin(uint8_t pin)
{
    const struct pin_index *index;

    if (pin < ITEM_NUM(pins))
    {
        index = &pins[pin];
        if (index->index == -1)
            index = RT_NULL;
    }
    else
    {
        index = RT_NULL;
    }

    return index;
};

void relay_write(uint8_t pin, GPIO_PinState value)
{
    const struct pin_index *index;

    index = get_pin(pin);
    if (index == RT_NULL)
        return;

    //    HAL_GPIO_WritePin(index->gpio, index->pin, value);

    if (value == GPIO_PIN_SET)
        index->gpio->BSRR = (uint32_t)index->pin;
    else
        index->gpio->BRR = (uint32_t)index->pin;
}
