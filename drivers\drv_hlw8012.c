#include "drv_hlw8012.h"
#include "app_process.h"

static struct channel_t channel[TOTAL_PORTS_NUM];

//static uint16_t U16_P_REF_Data;				//参考功率值,如以1000W校正。1000.0W
//static uint16_t U16_V_REF_Data;				//参考电压  220.0V
//static uint16_t U16_I_REF_Data;				//参考电流  1000W,220V条件下是4.545A

//获取上个测试周期的模式
bool get_power_last_testonecycle_mode(uint8_t x)
{
    return channel[x].P.B_P_Last_TestOneCycle_Mode;
}

//获取上个周期的计时值
uint16_t get_power_last_onecycle_time(uint8_t x)
{
    return channel[x].P.U16_P_Last_OneCycleTime;
}

//获取上个周期的中断计数值
uint16_t get_power_last_cnt(uint8_t x)
{
    return channel[x].P.U16_P_Last_CNT;
}
//获取当前端口的电量值
uint16_t get_channel_energy_value(uint8_t x)
{
    return channel[x].C.U32_AC_E;
}

//设置当前端口的电量值
void set_channel_energy_value(uint8_t x, uint32_t temp)
{
    channel[x].C.U32_AC_E = temp;
    ;
}

//设置电量脉冲计数器
void set_channel_energy_pluse_cnt(uint8_t x, uint16_t temp)
{
    channel[x].C.U16_E_Pluse_CNT = temp;
}

//获取当前端口的功率值
uint16_t get_channel_power_value(uint8_t x)
{
    return channel[x].P.U16_AC_P;
}

//设置当前端口的功率值
void set_channel_power_value(uint8_t x, uint32_t temp)
{
    channel[x].P.U16_AC_P = temp;
    ;
}

//hlw8012参数初始化
void init_charge_rom(uint8_t x, uint8_t mode)
{
    uint16_t freq_temp = 0, energy_temp = 0, over_temp = 0;
    uint16_t temp = 0;

    freq_temp = get_calibrate_ref_freq_value(x);
    energy_temp = get_calibrate_ref_energy_value(x);
    over_temp = get_calibrate_ref_over_value();

    if (freq_temp != 0)
    {
        temp = 1 << x;
        if ((over_temp & temp) != 0)
            channel[x].Ref.U32_P_REF_PLUSEWIDTH_TIME = freq_temp + 0xffff;
        else
            channel[x].Ref.U32_P_REF_PLUSEWIDTH_TIME = freq_temp;
    }

    channel[x].Ref.U16_REF_001_E_Pluse_CNT = energy_temp;

    channel[x].C.U8_CURR_WorkMode = mode;

    channel[x].P.U16_AC_P = 0;
    //		channel[i].V.U16_AC_V = 0;
    //		channel[i].I.U16_AC_I = 0;
    //		channel[i].C.U8_AC_COS = 0;

    channel[x].P.U16_P_TotalTimes = 0;
    //		channel[i].V.U16_V_TotalTimes = 0;
    //		channel[i].I.U16_I_TotalTimes = 0;

    channel[x].P.U16_P_OneCycleTime = 0;
    //		channel[i].V.U16_V_OneCycleTime = 0;
    //		channel[i].I.U16_I_OneCycleTime = 0;
    channel[x].P.U16_P_Last_OneCycleTime = 0;
    //		channel[i].V.U16_V_Last_OneCycleTime = 0;
    //		channel[i].I.U16_I_Last_OneCycleTime = 0;

    channel[x].P.U16_P_CNT = 0;
    //		channel[i].V.U16_V_CNT = 0;
    //		channel[i].I.U16_I_CNT = 0;
    channel[x].P.U16_P_Last_CNT = 0;
    //		channel[i].V.U16_V_Last_CNT = 0;
    //		channel[i].I.U16_I_Last_CNT = 0;

    //初始化单周期测量模式
    channel[x].P.B_P_TestOneCycle_Mode = 1;
    //		channel[i].V.B_V_TestOneCycle_Mode = true;
    //		channel[i].I.B_I_TestOneCycle_Mode = true;
    channel[x].P.B_P_Last_TestOneCycle_Mode = 1;
    //		channel[i].V.B_V_Last_TestOneCycle_Mode= true;
    //		channel[i].I.B_I_Last_TestOneCycle_Mode = true;

    //开始测量，置溢出标志位为1
    channel[x].P.B_P_OVERFLOW = 1;
    //		channel[i].V.B_V_OVERFLOW = true;
    //		channel[i].I.B_I_OVERFLOW = true;

    channel[x].P.B_P_Last_OVERFLOW = 1;
    //		channel[i].V.B_V_Last_OVERFLOW = true;
    //		channel[i].I.B_I_Last_OVERFLOW = true;

    channel[x].C.U32_AC_E = 0;
    channel[x].C.U16_E_Pluse_CNT = 0;
}

static void CF_Interrupt(uint8_t x)
{
    //功率测量
    channel[x].P.U16_P_TotalTimes = 0; //完成一次有效的测量，溢出寄存器清零
    channel[x].P.U16_P_CNT++;
    if (channel[x].P.B_P_OVERFLOW != 0)
    {
        //从溢出模式转入,开始测量
        channel[x].P.B_P_TestOneCycle_Mode = 0; //初始化为计数脉冲测量模式
        channel[x].P.U16_P_TotalTimes = 0;      //清溢出寄存器清零
        channel[x].P.U16_P_OneCycleTime = 0;    //清测量寄存器
        channel[x].P.U16_P_CNT = 1;
        channel[x].P.B_P_OVERFLOW = 0; //清溢出标志位
    }
    else
    {
        if (channel[x].P.B_P_TestOneCycle_Mode == 1)
        {
            if (channel[x].P.U16_P_OneCycleTime >= D_TIME1_100MS)
            {
                //单周期测量模式
                channel[x].P.U16_P_Last_OneCycleTime = channel[x].P.U16_P_OneCycleTime;
                channel[x].P.B_P_Last_TestOneCycle_Mode = channel[x].P.B_P_TestOneCycle_Mode;
                channel[x].P.B_P_OVERFLOW = 0; //溢出标志位清零
                channel[x].P.B_P_Last_OVERFLOW = channel[x].P.B_P_OVERFLOW;
                //清状态参数,重新开始测试
                channel[x].P.B_P_TestOneCycle_Mode = 0; //初始化为计数脉冲测量模式
                channel[x].P.U16_P_TotalTimes = 0;      //完成一次有效的测量，溢出寄存器清零
                channel[x].P.U16_P_OneCycleTime = 0;    //清测量寄存器
                channel[x].P.U16_P_CNT = 1;
            }
        }
        else
        {
            if (channel[x].P.U16_P_OneCycleTime >= D_TIME1_3S)
            {
                channel[x].P.U16_P_Last_OneCycleTime = channel[x].P.U16_P_OneCycleTime;
                channel[x].P.U16_P_Last_CNT = channel[x].P.U16_P_CNT;
                channel[x].P.B_P_Last_TestOneCycle_Mode = channel[x].P.B_P_TestOneCycle_Mode;
                channel[x].P.B_P_OVERFLOW = 0; //溢出标志位清零
                channel[x].P.B_P_Last_OVERFLOW = channel[x].P.B_P_OVERFLOW;
                //清状态参数,重新开始测试
                channel[x].P.B_P_TestOneCycle_Mode = 0; //初始化为计数脉冲测量模式
                channel[x].P.U16_P_TotalTimes = 0;      //完成一次有效的测量，溢出寄存器清零
                channel[x].P.U16_P_OneCycleTime = 0;    //清测量寄存器
                channel[x].P.U16_P_CNT = 1;
            }
        }
    }

    //校正模式
    if (channel[x].C.U8_CURR_WorkMode == D_CAL_START_MODE)
    {
        //记录单位时间内的用电量
        channel[x].C.U16_E_Pluse_CNT++;
    }

    //用电量计量，每0.01度电，用电量寄存器增加0.01度
    if (channel[x].C.U8_CURR_WorkMode == D_NORMAL_MODE)
    {
        channel[x].C.U16_E_Pluse_CNT++;
        if (channel[x].C.U16_E_Pluse_CNT == channel[x].Ref.U16_REF_001_E_Pluse_CNT)
        {
            channel[x].C.U16_E_Pluse_CNT = 0;
            channel[x].C.U32_AC_E++;
        }
    }
}

//static void CF1_Interrupt(uint8_t port_num)
//{
//	//电压测试模式
//    if (channel[port_num].B_VI_Test_Mode == 1)
//    {
//        channel[port_num].U16_V_TotalTimes = 0;
//        channel[port_num].U16_V_CNT++;
//        if (channel[port_num].B_V_OVERFLOW == true)
//        {
//            //从溢出模式转入,开始测量
//            channel[port_num].B_V_TestOneCycle_Mode = 0;  //初始化为计数脉冲测量模式
//            channel[port_num].U16_V_TotalTimes = 0;       //清溢出寄存器清零
//            channel[port_num].U16_V_OneCycleTime = 0;     //清测量寄存器
//            channel[port_num].U16_V_CNT = 1;
//            channel[port_num].B_V_OVERFLOW = 0;       //清溢出标志位
//        }
//        else
//        {
//            if (channel[port_num].B_V_TestOneCycle_Mode == 1)
//            {
//                if (channel[port_num].U16_V_OneCycleTime >= D_TIME1_100MS)
//                {
//                    //单周期测量模式
//                    channel[port_num].U16_V_Last_OneCycleTime = channel[port_num].U16_V_OneCycleTime;
//                    channel[port_num].B_V_Last_TestOneCycle_Mode = channel[port_num].B_V_TestOneCycle_Mode;
//                    channel[port_num].B_V_OVERFLOW = 0;       //溢出标志位清零
//                    channel[port_num].B_V_Last_OVERFLOW = channel[port_num].B_V_OVERFLOW;
//                     //清状态参数,重新开始测试
//                    channel[port_num].B_V_TestOneCycle_Mode = 0;  //初始化为计数脉冲测量模式
//                    channel[port_num].U16_V_TotalTimes = 0;       //完成一次有效的测量，溢出寄存器清零
//                    channel[port_num].U16_V_OneCycleTime = 0;     //清测量寄存器
//                    channel[port_num].U16_V_CNT = 1;
//                }
//            }
//            else
//            {
//                if (channel[port_num].U16_V_OneCycleTime >= D_TIME1_200MS)
//                {
//                    channel[port_num].U16_V_Last_OneCycleTime = channel[port_num].U16_V_OneCycleTime;
//                    channel[port_num].U16_V_Last_CNT = channel[port_num].U16_V_CNT;
//                    channel[port_num].B_V_Last_TestOneCycle_Mode = channel[port_num].B_V_TestOneCycle_Mode;
//                    channel[port_num].B_V_OVERFLOW = 0;       //溢出标志位清零
//                    channel[port_num].B_V_Last_OVERFLOW = channel[port_num].B_V_OVERFLOW;
//                    //清状态参数,重新开始测试
//                    channel[port_num].B_V_TestOneCycle_Mode = 0;  //初始化为计数脉冲测量模式
//                    channel[port_num].U16_V_TotalTimes = 0;       //完成一次有效的测量，溢出寄存器清零
//                    channel[port_num].U16_V_OneCycleTime = 0;     //清测量寄存器
//                    channel[port_num].U16_V_CNT = 1;
//                    channel[port_num].B_V_OVERFLOW = 0;       //溢出标志位清零
//                }
//            }
//        }
//     }
//
//	//电流测试模式
//    if (channel[port_num].B_VI_Test_Mode == 0)
//    {
//        channel[port_num].U16_I_TotalTimes = 0;
//        channel[port_num].U16_I_CNT++;
//        if (channel[port_num].B_I_OVERFLOW == true)
//        {
//            //从溢出模式转入,开始测量
//            channel[port_num].B_I_TestOneCycle_Mode = 0;  //初始化为计数脉冲测量模式
//            channel[port_num].U16_I_TotalTimes = 0;       //清溢出寄存器清零
//            channel[port_num].U16_I_OneCycleTime = 0;     //清测量寄存器
//            channel[port_num].U16_I_CNT = 1;
//            channel[port_num].B_I_OVERFLOW = 0;       //清溢出标志位
//        }
//        else
//        {
//            if (channel[port_num].B_I_TestOneCycle_Mode == 1)
//            {
//                if (channel[port_num].U16_I_OneCycleTime >= D_TIME1_100MS)
//                {
//                    //单周期测量模式
//                    channel[port_num].U16_I_Last_OneCycleTime = channel[port_num].U16_I_OneCycleTime;
//                    channel[port_num].B_I_Last_TestOneCycle_Mode = channel[port_num].B_I_TestOneCycle_Mode;
//                    channel[port_num].B_I_OVERFLOW = 0;       //溢出标志位清零
//                    channel[port_num].B_I_Last_OVERFLOW = channel[port_num].B_I_OVERFLOW;
//                     //清状态参数,重新开始测试
//                    channel[port_num].B_I_TestOneCycle_Mode = 0;  //初始化为计数脉冲测量模式
//                    channel[port_num].U16_I_TotalTimes = 0;       //完成一次有效的测量，溢出寄存器清零
//                    channel[port_num].U16_I_OneCycleTime = 0;     //清测量寄存器
//                    channel[port_num].U16_I_CNT = 1;
//                }
//            }
//            else
//            {
//                if (channel[port_num].U16_I_OneCycleTime >= D_TIME1_1S)
//                {
//                    channel[port_num].U16_I_Last_OneCycleTime = channel[port_num].U16_I_OneCycleTime;
//                    channel[port_num].U16_I_Last_CNT = channel[port_num].U16_I_CNT;
//                    channel[port_num].B_I_Last_TestOneCycle_Mode = channel[port_num].B_I_TestOneCycle_Mode;
//                    channel[port_num].B_I_OVERFLOW = 0;       //溢出标志位清零
//                    channel[port_num].B_I_Last_OVERFLOW = channel[port_num].B_I_OVERFLOW;
//                    //清状态参数,重新开始测试
//                    channel[port_num].B_I_TestOneCycle_Mode = 0;  //初始化为计数脉冲测量模式
//                    channel[port_num].U16_I_TotalTimes = 0;       //完成一次有效的测量，溢出寄存器清零
//                    channel[port_num].U16_I_OneCycleTime = 0;     //清测量寄存器
//                    channel[port_num].U16_I_CNT = 1;
//                }
//            }
//        }
//    }
//}

static void TM_UPD_Interrupt(uint8_t x)
{
    uint16_t temp;
    uint16_t ref_value;
    //校正模式
    if (channel[x].C.U8_CURR_WorkMode == D_CAL_START_MODE)
    {
        channel[x].C.U32_Cal_Times++; //校正时间36S，1000W负载36S时间消耗0.01度电
        if (channel[x].C.U32_Cal_Times == D_TIME1_CAL_TIME)
        {
            channel[x].C.U32_Cal_Times = 0;
            channel[x].C.U8_CURR_WorkMode = D_CAL_END_MODE;
            //校准结束，根据参考校准功率计算比值
            temp = get_calibrate_ref_power_value();
            if (temp < 1000)
            {
                ref_value = 1000 / temp;
                ref_value *= channel[x].C.U16_E_Pluse_CNT; //记录36S时间内的脉冲数，此脉冲数表示0.01度用电量
            }
            else
            {
                ref_value = 100000 / temp;
                ref_value = (channel[x].C.U16_E_Pluse_CNT * ref_value) / 100; //记录36S时间内的脉冲数，此脉冲数表示0.01度用电量
            }
            channel[x].C.U16_E_Pluse_CNT = 0;
            channel[x].Ref.U16_REF_001_E_Pluse_CNT = ref_value;
            set_ref_energy_value(x, channel[x].Ref.U16_REF_001_E_Pluse_CNT);
            set_calibrate_complete_state(1);
        }
    }

    //功率测量
    if (channel[x].P.U16_P_CNT != 0)
    {
        channel[x].P.U16_P_OneCycleTime++;
        channel[x].P.U16_P_TotalTimes++;
    }
    if (channel[x].P.U16_P_TotalTimes >= D_TIME1_P_OVERFLOW)
    {
        channel[x].P.B_P_OVERFLOW = 1; //溢出，
        channel[x].P.B_P_Last_OVERFLOW = channel[x].P.B_P_OVERFLOW;
        //清状态参数,重新开始测试
        channel[x].P.U16_P_TotalTimes = 0; //清溢出寄存器
        channel[x].P.U16_P_OneCycleTime = 0;
        channel[x].P.U16_P_CNT = 0;             //等待下一次中断开始计数
        channel[x].P.B_P_TestOneCycle_Mode = 0; //初始化为计数脉冲测量模式
    }
    else if (channel[x].P.U16_P_OneCycleTime == D_TIME1_100MS)
    {
        if (channel[x].P.U16_P_CNT < 2)
        {
            // 100ms内只有一次中断，说明周期>100ms,采用单周期测量模式
            channel[x].P.B_P_TestOneCycle_Mode = 1;
        }
        else
        {
            // 100ms内有2次或以上数量脉冲，说明周期<100ms，采用计数脉冲测量模式
            channel[x].P.B_P_TestOneCycle_Mode = 0;
        }
    }

    //	//电压、电流测量
    //	if (channel[port_num].B_VI_Test_Mode == 1)
    //	{
    //		//电压测量
    //		if (channel[port_num].U16_V_CNT != 0)
    //		{
    //			channel[port_num].U16_V_OneCycleTime++;
    //			channel[port_num].U16_V_TotalTimes++;
    //		}
    //		if (channel[port_num].U16_V_TotalTimes >= D_TIME1_V_OVERFLOW)
    //		{
    //			channel[port_num].B_V_OVERFLOW = true;
    //			channel[port_num].B_V_Last_OVERFLOW = channel[port_num].B_V_OVERFLOW;
    //			//清状态参数,重新开始测试
    //			channel[port_num].U16_V_TotalTimes = 0;       //清溢出寄存器
    //			channel[port_num].U16_V_OneCycleTime = 0;
    //			channel[port_num].U16_V_CNT = 0;
    //			channel[port_num].B_V_TestOneCycle_Mode = 0;   //初始化为计数脉冲测量模式
    //		}
    //		else if (channel[port_num].U16_V_OneCycleTime == D_TIME1_100MS)
    //		{
    //			if (channel[port_num].U16_V_CNT < 2)
    //			{
    //				// 100ms内只有一次中断，说明周期>100ms,采用单周期测量模式
    //				channel[port_num].B_V_TestOneCycle_Mode = 1;
    //			}
    //			else
    //			{
    //				// 100ms内有2次或以上数量脉冲，说明周期<100ms，采用计数脉冲测量模式
    //				channel[port_num].B_V_TestOneCycle_Mode = 0;
    //			}
    //		}
    //	}
    //	else
    //	{
    //		//电流测量
    //		if (channel[port_num].U16_I_CNT != 0)
    //		{
    //			channel[port_num].U16_I_OneCycleTime++;
    //			channel[port_num].U16_I_TotalTimes++;
    //		}
    //		if (channel[port_num].U16_I_TotalTimes >= D_TIME1_I_OVERFLOW)
    //		{
    //			channel[port_num].B_I_OVERFLOW = true;
    //			channel[port_num].B_I_Last_OVERFLOW = channel[port_num].B_I_OVERFLOW;
    //			//清状态参数,重新开始测试
    //			channel[port_num].U16_I_TotalTimes = 0;       //清溢出寄存器
    //			channel[port_num].U16_I_OneCycleTime = 0;
    //			channel[port_num].U16_I_CNT = 0;
    //			channel[port_num].B_I_TestOneCycle_Mode = 0;   //初始化为计数脉冲测量模式
    //		}
    //		else if (channel[port_num].U16_I_OneCycleTime == D_TIME1_100MS)
    //		{
    //			if (channel[port_num].U16_I_CNT < 2)
    //			{
    //				// 100ms内只有一次中断，说明周期>100ms,采用单周期测量模式
    //				channel[port_num].B_I_TestOneCycle_Mode = 1;
    //			}
    //			else
    //			{
    //				// 100ms内有2次或以上数量脉冲，说明周期<100ms，采用计数脉冲测量模式
    //				channel[port_num].B_I_TestOneCycle_Mode = 0;
    //			}
    //		}
    //	}
    //
    //	//电压、电流测量模式切换  B_VI_Test_Mode:(1:电压测量模式) (0:电流测试模式)
    //	channel[port_num].U16_VI_Test_Times--;
    //	if (channel[port_num].U16_VI_Test_Times == 0)
    //	{
    //		if (channel[port_num].B_VI_Test_Mode == 1)
    //		{
    //			//转为电流测量模式
    //			channel[port_num].B_VI_Test_Mode = 0;
    //			//            IO_channel_CF1_S = 0;
    //			channel[port_num].U16_VI_Test_Times = D_TIME1_10S;
    //
    //			//清状态参数
    //			channel[port_num].U16_I_TotalTimes = 0;
    //			channel[port_num].U16_I_OneCycleTime = 0;
    //			channel[port_num].U16_I_CNT = 0;
    //			channel[port_num].B_I_OVERFLOW = 0;
    //		}
    //		else
    //		{
    //			//转为电压测量模式
    //			channel[port_num].B_VI_Test_Mode = 1;
    //			//            IO_channel_CF1_S = 1;
    //			channel[port_num].U16_VI_Test_Times = D_TIME1_1S;
    //
    //			//清状态参数
    //			channel[port_num].U16_V_TotalTimes = 0;
    //			channel[port_num].U16_V_OneCycleTime = 0;
    //			channel[port_num].U16_V_CNT = 0;
    //			channel[port_num].B_V_OVERFLOW = 0;
    //		}
    //	}
}

/*-------------------------------------------- 功率、电压、电流计算 -------------------------------------------*/
/*=====================================================
 * Function : void channel_Measure_P(void)
 * Describe : 
 * Input    : none
 * Output   : none
 * Return   : none
 * Record   : 2014/04/14
=====================================================*/
uint16_t channel_Measure_P(uint8_t x)
{
    uint32_t a = 0;
    uint32_t b = 0;
    uint32_t u32_P_Period = 0;

    uint16_t power_value = 0;

    if (channel[x].P.B_P_Last_TestOneCycle_Mode == 1)
    {
        //单周期测量模式
        b = channel[x].P.U16_P_Last_OneCycleTime;
        b = b * 1000; //ms转换成us
        u32_P_Period = b;
    }
    else
    {
        //计数脉冲测量模式
        b = channel[x].P.U16_P_Last_OneCycleTime;
        b = b * 1000;
        u32_P_Period = b / (channel[x].P.U16_P_Last_CNT - 1);
    }

    channel[x].Ref.U32_P_CURRENT_PLUSEWIDTH_TIME = u32_P_Period; // 校正时取U32_P_CURRENT_PLUSEWIDTH_TIME参数作为参考值
    a = get_calibrate_ref_power_value() * channel[x].Ref.U32_P_REF_PLUSEWIDTH_TIME;
    power_value = a / channel[x].Ref.U32_P_CURRENT_PLUSEWIDTH_TIME;

    if (power_value == 0xffff) //开机时U32_P_CURRENT_PLUSEWIDTH_TIME = 0，计算溢出
    {
        power_value = 0;
    }

    if (channel[x].P.B_P_Last_OVERFLOW != 0)
    {
        power_value = 0;
    }

    return power_value;
}
/*=====================================================
 * Function : void channel_Measure_V(void)
 * Describe : 
 * Input    : none
 * Output   : none
 * Return   : none
 * Record   : 2014/04/14
=====================================================*/
//static void channel_Measure_V(uint8_t port_num)
//{
//    u32 a;
//    u32 b;
//    u32 u32_V_Period;
//
//    if (channel[port_num].U16_VI_Test_Times < D_TIME1_100MS)
//    {
//        if (channel[port_num].B_V_Last_TestOneCycle_Mode == 1)
//        {
//            b = channel[port_num].U16_V_Last_OneCycleTime;
//            u32_V_Period = b * 1000;   //ms转换成us
//            channel[port_num].U32_V_CURRENT_PLUSEWIDTH_TIME = u32_V_Period;
//        }
//        else
//        {
//             b = channel[port_num].U16_V_Last_OneCycleTime;
//             b = b*1000;
//             u32_V_Period = b/(channel[port_num].U16_V_Last_CNT-1);
//             //u32_V_Period = b/U16_V_Last_CNT;
//             channel[port_num].U32_V_CURRENT_PLUSEWIDTH_TIME = u32_V_Period;
//        }
//    }
//
//    a = U16_V_REF_Data * channel[port_num].U32_V_REF_PLUSEWIDTH_TIME;
//    channel[port_num].U16_AC_V = a/channel[port_num].U32_V_CURRENT_PLUSEWIDTH_TIME;
//
//    if (channel[port_num].U16_AC_V == 0xffff)     //开机时U32_V_CURRENT_PLUSEWIDTH_TIME = 0，计算溢出
//    {
//        channel[port_num].U16_AC_V = 0;
//    }
//
//    if (channel[port_num].B_V_Last_OVERFLOW == true)
//    {
//        channel[port_num].U16_AC_V = 0;
//    }
//
//    if (channel[port_num].U16_AC_P == 0)
//    {
//        channel[port_num].U16_AC_V = 0;
//    }
//}
/*=====================================================
 * Function : void channel_Measure_I(void)
 * Describe : 
 * Input    : none
 * Output   : none
 * Return   : none
 * Record   : 2014/04/14
=====================================================*/
//static void channel_Measure_I(uint8_t port_num)
//{
//    u32 a;
//    u32 b;
//    u32 u32_I_Period;
//
//    if (channel[port_num].U16_VI_Test_Times < D_TIME1_6S)
//    {
//        if (channel[port_num].B_I_Last_TestOneCycle_Mode == 1)
//        {
//
//            b = channel[port_num].U16_I_Last_OneCycleTime;
//            u32_I_Period = b * 1000;   //ms转换成us
//            channel[port_num].U32_I_CURRENT_PLUSEWIDTH_TIME = u32_I_Period;
//        }
//        else
//        {
//             b = channel[port_num].U16_I_Last_OneCycleTime;
//             b = b*1000;
//             u32_I_Period = b/(channel[port_num].U16_I_Last_CNT-1);
//             //u32_I_Period = b/U16_I_Last_CNT;
//             channel[port_num].U32_I_CURRENT_PLUSEWIDTH_TIME = u32_I_Period;
//        }
//    }
//
//
//    a = U16_I_REF_Data * channel[port_num].U32_I_REF_PLUSEWIDTH_TIME;
//    channel[port_num].U16_AC_I = a/channel[port_num].U32_I_CURRENT_PLUSEWIDTH_TIME;
//
//
//    if (channel[port_num].U16_AC_I > 180)
//    {
//       if (channel[port_num].U16_AC_I != 0xffff)
//       {
//            asm("nop");
//            asm("nop");
//       }
//    }
//
//    if (channel[port_num].U16_AC_P == 0)
//    {
//        channel[port_num].U16_AC_I = 0;
//    }
//
//    if (channel[port_num].U16_AC_I == 0xffff)     //开机时U32_I_CURRENT_PLUSEWIDTH_TIME = 0，计算溢出
//    {
//        channel[port_num].U16_AC_I = 0;
//    }
//
//    if (channel[port_num].B_I_OVERFLOW == true)
//    {
//        channel[port_num].U16_AC_I = 0;
//    }
//}
/*=====================================================
 * Function : void channel_Measure_COS(void)
 * Describe : 
 * Input    : none
 * Output   : none
 * Return   : none
 * Record   : 2014/04/14
=====================================================*/
//static void channel_Measure_COS(uint8_t port_num)
//{
//	u32 u32_P_Apparent;
//	u32 a;
//	u8 b;
//	u32_P_Apparent = (u32)channel[port_num].U16_AC_I*(u32)channel[port_num].U16_AC_V;
//	u32_P_Apparent = u32_P_Apparent/1000;
//	a = channel[port_num].U16_AC_P;
//	a = a*100;
//
//	b = (u8)(a/u32_P_Apparent);
//	if (b > 100 )
//		channel[port_num].U8_AC_COS = 100; 		//由于相位延时造成的功率因素>1的情况，功率因素取值1
//	else
//		channel[port_num].U8_AC_COS = b;
//}
/*=====================================================
 * Function : void channel_Measure_E(void)
 * Describe : 每1度电存储一次
 * Input    : none
 * Output   : none
 * Return   : none
 * Record   : 2014/04/14
=====================================================*/
//static void channel_Measure_E(uint8_t x)
//{
////	if (channel[port_num].U32_AC_BACKUP_E < channel[port_num].U32_AC_E)
////	{
////		channel[port_num].U32_AC_BACKUP_E = channel[port_num].U32_AC_E;
////	}
//}

static uint8_t get_index(uint16_t gpio_pin)
{
    uint8_t i;

    switch (gpio_pin)
    {
    case GPIO_PIN_6:
        if (HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_6) == GPIO_PIN_RESET)
            i = 7;
        break;
    case GPIO_PIN_7:
        if (HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_7) == GPIO_PIN_RESET)
            i = 8;
        break;
    case GPIO_PIN_8:
        if (HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_8) == GPIO_PIN_RESET)
            i = 9;
        break;
    case GPIO_PIN_9:
        if (HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_9) == GPIO_PIN_RESET)
            i = 10;
        break;
    case GPIO_PIN_10:
        if (HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_10) == GPIO_PIN_RESET)
            i = 1;
        break;
    case GPIO_PIN_11:
        if (HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_11) == GPIO_PIN_RESET)
            i = 2;
        break;
    case GPIO_PIN_12:
        if (HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_12) == GPIO_PIN_RESET)
            i = 3;
        break;
    case GPIO_PIN_13:
        if (HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_13) == GPIO_PIN_RESET)
            i = 4;
        break;
    case GPIO_PIN_14:
        if (HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_14) == GPIO_PIN_RESET)
            i = 5;
        break;
    case GPIO_PIN_15:
        if (HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_15) == GPIO_PIN_RESET)
            i = 6;
        break;
        //        case GPIO_PIN_4:
        //            if(HAL_GPIO_ReadPin(GPIOF, GPIO_PIN_4) == GPIO_PIN_RESET)
        //                i = 1;
        //            else if(HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_4) == GPIO_PIN_RESET)
        //                i = 3;
        //            break;
        //        case GPIO_PIN_5:
        //            if(HAL_GPIO_ReadPin(GPIOF, GPIO_PIN_5) == GPIO_PIN_RESET)
        //                i = 2;
        //            else if(HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_5) == GPIO_PIN_RESET)
        //                i = 4;
        //            break;
        //        case GPIO_PIN_6:
        //            if(HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_6) == GPIO_PIN_RESET)
        //                i = 5;
        //            break;
        //        case GPIO_PIN_7:
        //            if(HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_7) == GPIO_PIN_RESET)
        //                i = 6;
        //            break;
    default:
        break;
    }

    return i;
}

//struct flag_t{
//    uint8_t state:1;
//};
//struct flag_t flag[TOTAL_PORTS_NUM];

void hlw8012_update_timer_count(void)
{
    uint8_t i = 0;

    for (i = 0; i < TOTAL_PORTS_NUM; i++)
    {
        TM_UPD_Interrupt(i);
    }
}

void hlw8012_update_exti_state(uint16_t GPIO_Pin)
{
    uint8_t index;

    index = get_index(GPIO_Pin);
    CF_Interrupt(index - 1);
}

void get_measure_power_value(void)
{
    uint8_t i = 0;
    static uint8_t measure_filter_cnt = 0;

    if (++measure_filter_cnt >= D_TIME1_100MS)
    {
        measure_filter_cnt = 0;

        for (i = 0; i < TOTAL_PORTS_NUM; i++)
        {
//            channel[i].P.U16_AC_P = mix_filter(i, channel_Measure_P(i));
			channel[i].P.U16_AC_P = channel_Measure_P(i);
        }
    }
}
