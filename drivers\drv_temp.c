#include "drv_temp.h"
#include "drv_common.h"

static const uint16_t NTCTAB[141] = {505, 527, 550, 574, 599, 624, 650, 677, 704, 732, 761, 791, 821, 851, 883, 915, 947, 981, 1014, 1049, 1084, 1119, 1155, 1191, 1228, 1265, 1303, 1341, 1379, 1417, 1456, 1495, 1535, 1574, 1613, 1653, 1693, 1733, 1772, 1812, 1852, 1891, 1930, 1970, 2009, 2048, 2087, 2126, 2164, 2201, 2238, 2276, 2313, 2350, 2386, 2421, 2457, 2491, 2525, 2560, 2592, 2626, 2658, 2689, 2722, 2753, 2783, 2811, 2840, 2870, 2899, 2926, 2953, 2979, 3005, 3032, 3057, 3080, 3105, 3129, 3151, 3173, 3195, 3218, 3238, 3259, 3279, 3298, 3317, 3336, 3355, 3371, 3388, 3405, 3422, 3439, 3454, 3471, 3486, 3501, 3513, 3528, 3540, 3556, 3568, 3580, 3593, 3602, 3615, 3628, 3638, 3647, 3660, 3670, 3680, 3690, 3700, 3707, 3717, 3727, 3734, 3741, 3751, 3758, 3765, 3775, 3782, 3789, 3796, 3803, 3807, 3814, 3821, 3828, 3832, 3839, 3846, 3850, 3857, 3861, 3864};

ADC_HandleTypeDef hadc;

void HAL_ADC_MspInit(ADC_HandleTypeDef *hadc)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    if (hadc->Instance == ADC1)
    {
        /* Peripheral clock enable */
        __HAL_RCC_ADC1_CLK_ENABLE();

        __HAL_RCC_GPIOC_CLK_ENABLE();
        /**ADC GPIO Configuration
        PC3     ------> ADC_IN13
        */
        GPIO_InitStruct.Pin = GPIO_PIN_3;
        GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

        /* ADC1 interrupt Init */
        //        HAL_NVIC_SetPriority(ADC1_IRQn, 0, 0);
        //        HAL_NVIC_EnableIRQ(ADC1_IRQn);
    }
}

/**
* @brief ADC MSP De-Initialization
* This function freeze the hardware resources used in this example
* @param hadc: ADC handle pointer
* @retval None
*/
void HAL_ADC_MspDeInit(ADC_HandleTypeDef *hadc)
{
    if (hadc->Instance == ADC1)
    {
        /* Peripheral clock disable */
        __HAL_RCC_ADC1_CLK_DISABLE();

        /**ADC GPIO Configuration
        PC3     ------> ADC_IN13
        */
        HAL_GPIO_DeInit(GPIOC, GPIO_PIN_3);

        /* ADC1 interrupt DeInit */
        HAL_NVIC_DisableIRQ(ADC1_IRQn);
    }
}

int adc_init(void)
{
    ADC_ChannelConfTypeDef sConfig = {0};

    hadc.Instance = ADC1;
    hadc.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    hadc.Init.Resolution = ADC_RESOLUTION_12B;
    hadc.Init.DataAlign = ADC_DATAALIGN_RIGHT;
    hadc.Init.ScanConvMode = ADC_SCAN_DIRECTION_FORWARD;
    hadc.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    hadc.Init.LowPowerAutoWait = DISABLE;
    hadc.Init.LowPowerAutoPowerOff = DISABLE;
    hadc.Init.ContinuousConvMode = DISABLE;
    hadc.Init.DiscontinuousConvMode = DISABLE;
    hadc.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    hadc.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
    hadc.Init.DMAContinuousRequests = DISABLE;
    hadc.Init.Overrun = ADC_OVR_DATA_OVERWRITTEN;

    if (HAL_ADC_DeInit(&hadc) != HAL_OK)
    {
        Error_Handler();
    }

    if (HAL_ADC_Init(&hadc) != HAL_OK)
    {
        Error_Handler();
    }
    /** Configure for the selected ADC regular channel to be converted.
    */
    sConfig.Channel = ADC_CHANNEL_13;
    sConfig.Rank = ADC_RANK_CHANNEL_NUMBER;
    sConfig.SamplingTime = ADC_SAMPLETIME_55CYCLES_5;
    if (HAL_ADC_ConfigChannel(&hadc, &sConfig) != HAL_OK)
    {
        Error_Handler();
    }

    if (HAL_ADCEx_Calibration_Start(&hadc) != HAL_OK) //AD校准
    {
        Error_Handler();
    }

    __HAL_ADC_CLEAR_FLAG(&hadc, ADC_FLAG_RDY);

    return RT_EOK;
}
INIT_BOARD_EXPORT(adc_init);

static uint8_t FindTab(uint8_t TabLong, uint16_t dat)
{
    uint8_t st, ed, m;
    uint8_t i;

    st = 0;
    ed = TabLong - 1;
    i = 0;

    if (dat >= NTCTAB[ed])
        return ed;
    else if (dat <= NTCTAB[st])
        return st;

    while (st < ed)
    {
        m = (st + ed) / 2;

        if (dat == NTCTAB[m])
            break;
        if (dat < NTCTAB[m + 1] && dat > NTCTAB[m])
            break;

        if (dat > NTCTAB[m])
            st = m; //ed = m ;
        else
            ed = m; //st = m ;

        if (i++ > TabLong)
            break;
    }

    if (st > ed)
        return 0;

    return m;
}

#define N 10
static uint16_t adc_sample_value[N];
static uint16_t filter_value = 0;
static uint8_t sample_cnt = 0;

static uint16_t middle_filter(void)
{
    uint16_t count, i, j, temp;

    for (j = 0; j < N - 1; j++)
    {
        for (i = 0; i < N - j; i++)
        {
            if (adc_sample_value[i] > adc_sample_value[i + 1])
            {
                temp = adc_sample_value[i];
                adc_sample_value[i] = adc_sample_value[i + 1];
                adc_sample_value[i + 1] = temp;
            }
        }
    }

    return adc_sample_value[(N - 1) / 2];
}

void get_adc_value(void)
{
    HAL_ADC_Start(&hadc);
    HAL_ADC_PollForConversion(&hadc, 10);
    if (HAL_IS_BIT_SET(HAL_ADC_GetState(&hadc), HAL_ADC_STATE_REG_EOC))
        adc_sample_value[sample_cnt] = HAL_ADC_GetValue(&hadc);
    HAL_ADC_Stop(&hadc);

    sample_cnt++;
    if (sample_cnt >= N)
    {
        sample_cnt = 0;
        filter_value = middle_filter();
    }
}

int get_temp(void)
{
    return FindTab(141, filter_value) - 20;
}
