#ifndef __DRV_HLW8012_H__
#define __DRV_HLW8012_H__

#include "stdbool.h"

#include "stm32f0xx.h"
#include "rtthread.h"

//Time1定时器定时,时间基数 = 1ms
#define D_TIME1_20MS			20		
#define D_TIME1_100MS			100	
#define D_TIME1_150MS			150	
#define D_TIME1_200MS			200	
#define D_TIME1_400MS			400	
#define D_TIME1_500MS			500	
#define D_TIME1_1S				1000		//Time1定时器定时1S时间常数
#define D_TIME1_2S				2000	
#define D_TIME1_3S				3000	
#define D_TIME1_4S				4000
#define D_TIME1_5S				5000
#define D_TIME1_6S				6000
#define D_TIME1_8S				8000
#define D_TIME1_9S				9000
#define D_TIME1_10S				10000
#define D_TIME1_20S				20000


#define D_TIME1_V_OVERFLOW      500        //Time1定时器,电压溢出常数设定为500mS,溢出说明脉宽周期大于500mS
#define D_TIME1_I_OVERFLOW		8000	   //Time1定时器,电流溢出常数设定为10S,溢出说明脉宽周期大于10S
#define D_TIME1_P_OVERFLOW		5000	   //Time1定时器,功率溢出常数设定为10S(约0.5W最小值),溢出说明脉宽周期大于10S
//#define D_TIME1_P_OVERFLOW		40000	   //Time1定时器,功率溢出常数设定为40S(约0.2W最小值)
#define D_TIME1_CAL_TIME			36000	   //校正时间，记录在此时间内的脉冲数，1000W负载在用电36S时间内耗费0.01度电

//工作模式
//--------------------------------------------------------------------------------------------
//--------------------------------------------------------------------------------------------
#define D_ERR_MODE           	0x00        //错误提示模式
#define D_NORMAL_MODE		   	0x10	    //正常工作模式
#define D_CAL_START_MODE		0x21	    //校正模式，启动
#define D_CAL_END_MODE			0x23	    //校正模式，完成

struct power{
    uint16_t U16_P_TotalTimes;			//当前脉冲 功率测量总时间
    uint16_t U16_P_OneCycleTime;			//功率测量时间参数
    uint16_t U16_P_Last_OneCycleTime;		//功率测量时间参数，上一次数量值
    uint16_t U16_P_CNT;				//功率测量脉冲数量
    uint16_t U16_P_Last_CNT;				//功率测量脉冲数量，上一次数量值
    uint16_t U16_AC_P;				//功率值 1000.0W
    
    uint8_t  B_P_TestOneCycle_Mode:1;			//功率测量模式 1:单周期测量，0:1S定时测量
    uint8_t  B_P_Last_TestOneCycle_Mode:1;
    uint8_t  B_P_OVERFLOW:1;       			// 功率脉冲周期 溢出标志位 
    uint8_t  B_P_Last_OVERFLOW:1;       		// 功率脉冲周期 溢出标志位 
	uint8_t :1;	
	uint8_t :1;	
	uint8_t :1;
	uint8_t :1;
};

//struct voltage{
//    uint16_t U16_V_TotalTimes;			//当前脉冲 电压测量总时间
//    uint16_t U16_V_OneCycleTime;			//电压测量时间参数
//    uint16_t U16_V_Last_OneCycleTime;		//电压测量时间参数，上一次数量值
//    uint16_t U16_V_CNT;				//电压测量脉冲数量
//    uint16_t U16_V_Last_CNT;				//电压测量脉冲数量，上一次数量值
//    uint16_t U16_AC_V;				//电压值 220.0V
//    uint8_t  B_V_TestOneCycle_Mode:1;
//    uint8_t  B_V_Last_TestOneCycle_Mode:1;
//    uint8_t  B_V_OVERFLOW:1;       			// 电压脉冲周期 溢出标志位
//    uint8_t  B_V_Last_OVERFLOW:1;       		// 电压脉冲周期 溢出标志位

//};

//struct current{
//    uint16_t U16_I_TotalTimes;			//当前脉冲 电流测量总时间
//    uint16_t U16_I_OneCycleTime;			//电流测量时间参数
//    uint16_t U16_I_Last_OneCycleTime;		//电流测量时间参数，上一次数量值
//    uint16_t U16_I_CNT;				//电流测量脉冲数量
//    uint16_t U16_I_Last_CNT;				//电流测量脉冲数量，上一次数量值
//    uint16_t U16_AC_I;				//电流值 4.545A
//    uint8_t     B_I_TestOneCycle_Mode:1;
//    uint8_t     B_I_Last_TestOneCycle_Mode:1;
//    uint8_t     B_I_OVERFLOW:1;       			// 电流脉冲周期 溢出标志位
//    uint8_t     B_I_Last_OVERFLOW:1;       		// 电流脉冲周期 溢出标志位

//};

struct reference{
    uint32_t U32_P_REF_PLUSEWIDTH_TIME;      	//参考功率 脉冲周期
    uint32_t U32_P_CURRENT_PLUSEWIDTH_TIME;      	//当前功率 脉冲周期
    uint32_t U16_REF_001_E_Pluse_CNT;        	//0.01度电脉冲总数参考值
};

struct cal{
	uint16_t U32_AC_E;				        //用电量   0.01度
	uint16_t U32_Cal_Times;                 //校正时间
//    uint16_t U16_VI_Test_Times;		
	uint16_t U16_E_Pluse_CNT;          	 	//脉冲个数寄存器
    uint8_t  U8_CURR_WorkMode;
//    bool     B_VI_Test_Mode;				//1:电压测量模式;0:电流测量模式
};

struct channel_t{
    struct power P;
//    voltage V;
//    current I;
    struct reference Ref;
    struct cal C;
};

extern void hlw8012_update_timer_count(void);
extern void hlw8012_update_exti_state(uint16_t GPIO_Pin);

extern void init_charge_rom(uint8_t x, uint8_t mode);
extern uint16_t channel_Measure_P(uint8_t x);

extern bool get_power_last_testonecycle_mode(uint8_t x);
extern uint16_t get_power_last_onecycle_time(uint8_t x);
extern uint16_t get_power_last_cnt(uint8_t x);
extern uint16_t get_channel_power_value(uint8_t x);
extern uint16_t get_channel_energy_value(uint8_t x);
extern void get_measure_power_value(void);

extern void set_channel_energy_pluse_cnt(uint8_t x, uint16_t temp);
extern void set_channel_energy_value(uint8_t x, uint32_t temp);
extern void set_channel_power_value(uint8_t x, uint32_t temp);

#endif
