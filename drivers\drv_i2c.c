#include "drv_i2c.h"
#include "drv_common.h"

#define EE_SLAVE_ADDRESS7 0xA0
#define EE_PAGESIZE 8   //24C02
#define EE_CHIPSIZE 256 //24C02

//重新设置SDA为上拉输入模式
//void I2C_SDA_IN(void)
//{
//    GPIO_InitTypeDef  GPIO_InitStructure;
//
//    GPIO_InitStructure.Pin = GPIO_PIN_9;
//    GPIO_InitStructure.Mode = GPIO_MODE_INPUT;  //上拉输入，使得板外部不需要接上拉电阻
//    GPIO_InitStructure.Pull = GPIO_NOPULL;
//    HAL_GPIO_Init(GPIOB, &GPIO_InitStructure);
//}

//重新设置SDA为推挽输出模式
//void I2C_SDA_OUT(void)
//{
//    GPIO_InitTypeDef  GPIO_InitStructure;
//
//    GPIO_InitStructure.Pin = GPIO_PIN_9;
//    GPIO_InitStructure.Mode = GPIO_MODE_OUTPUT_OD;  //推挽输出
//    GPIO_InitStructure.Pull = GPIO_NOPULL;
//    GPIO_InitStructure.Speed = GPIO_SPEED_FREQ_HIGH; //IO口速度为50MHz
//    HAL_GPIO_Init(GPIOB, &GPIO_InitStructure);
//}

// 发送IIC起始信号
bool I2Cx_Start(void)
{
    //    I2C_SDA_OUT();

    Pin_SCL_H; // 拉高时钟线
    Pin_SDA_H; // 拉高信号线
    rt_hw_us_delay(5);
    Pin_SDA_L;
    rt_hw_us_delay(5);
    Pin_SCL_L;
    rt_hw_us_delay(2);

    return true;
}

// 发送IIC停止信号
void I2Cx_Stop(void)
{
    //    I2C_SDA_OUT();

    Pin_SDA_L;
    Pin_SCL_H;
    rt_hw_us_delay(5);
    Pin_SDA_H;
    rt_hw_us_delay(5);
}

// IIC发送ACK信号
void I2Cx_Ack(void)
{
    //    I2C_SDA_OUT();

    Pin_SCL_L;
    Pin_SDA_L;
    rt_hw_us_delay(2);
    Pin_SCL_H;
    rt_hw_us_delay(5);
    Pin_SCL_L;
    rt_hw_us_delay(2);
}

// IIC不发送ACK信号
void I2Cx_NAck(void)
{
    //    I2C_SDA_OUT();

    Pin_SCL_L;
    Pin_SDA_H;
    rt_hw_us_delay(2);
    Pin_SCL_H;
    rt_hw_us_delay(5);
    Pin_SCL_L;
    rt_hw_us_delay(2);
}

// IIC等待ACK信号
bool I2Cx_Wait_Ack(void)
{
    uint8_t ucErrTime = 0;

    //    I2C_SDA_OUT();
    //释放SDA（置1），然后等待接收方应答将它拉低。确切的说，应是24C02发送字节最后一位的第8个时钟周期下降沿后经tAA
    //（SCL变低到SDA OUT有效的时间）约0.1-4.5us后拉低SDA，并随第9个时钟后结束。所以24C02正常时，SDA为1并不体现
    //（第8脉冲后马上被拉低了），但若器件坏了，就需要靠这个置1后不变来判断！（若不置1而上次发的数据最后一位为0就不好判断了）
    //从24C02的Block Diagram看，它只能在SDA为1时通过控制内部的Dout来把SDA拉低，但不能在SDA为0时将其置高！故主机要常将SDA置1，而SCl置0。
    Pin_SCL_L;
    Pin_SDA_H;
    rt_hw_us_delay(2);
    Pin_SCL_H; //WriteI2CByte中写完一字节后又将SCL拉低，这里拉高产生第9个时钟上升沿，然后在SCL为高期间对SDA进行检测
    rt_hw_us_delay(2);

    //    I2C_SDA_IN();
    while (Read_SDA_Pin && ucErrTime < 5)
    {
        ucErrTime++;
        rt_hw_us_delay(1);
    }
    if (ucErrTime >= 5)
    {
        I2Cx_Stop();
        return false;
    }
    Pin_SCL_L;
    rt_hw_us_delay(2);

    return true;
}

// IIC发送一个字节
void I2Cx_Send_Byte(uint8_t txd)
{
    uint8_t i = 0;
    /*时钟信号拉低，数据准备好再拉高进行传输*/
    //    I2C_SDA_OUT();

    Pin_SCL_L;
    for (i = 0; i < 8; i++)
    {
        if (txd & 0x80)
            Pin_SDA_H;
        else
            Pin_SDA_L;
        txd <<= 1;
        rt_hw_us_delay(2);
        /*SCL拉高传输数据*/
        Pin_SCL_H;
        rt_hw_us_delay(2);
        Pin_SCL_L;
    }
}

// IIC读取一个字节
uint8_t I2Cx_Read_Byte(void)
{
    uint8_t i = 0, rxd = 0;

    Pin_SDA_H;
    //    I2C_SDA_IN();
    for (i = 0; i < 8; i++)
    {
        Pin_SCL_L;
        rt_hw_us_delay(2);
        /*拉高SCL产生一个有效的时钟信号*/
        Pin_SCL_H;
        rt_hw_us_delay(2);
        /*读取总线上的数据*/
        rxd <<= 1;
        if (Read_SDA_Pin)
        {
            rxd |= 0x01;
        }
        rt_hw_us_delay(2);
    }
    Pin_SCL_L;
    rt_hw_us_delay(2);

    return rxd;
}

/**
* @brief  向24C02写数据
* @param  Data--数据
* @param  Address--地址
* @param  None
* @retval None
* @example 
**/
bool EE_Write_Data(uint16_t addr, uint8_t data)
{
    I2Cx_Start();
    I2Cx_Send_Byte(EE_SLAVE_ADDRESS7);
    I2Cx_Wait_Ack();

    I2Cx_Send_Byte((uint8_t)addr);
    I2Cx_Wait_Ack();

    I2Cx_Send_Byte(data);
    I2Cx_Wait_Ack();

    I2Cx_Stop();

    return true;
}

/**
* @brief  从24C02读出数据
* @param  None
* @param  Address--地址
* @param  None
* @retval 读到的数据
* @example 
**/
uint8_t EE_Read_Data(uint16_t addr)
{
    uint8_t data;

    I2Cx_Start();
    I2Cx_Send_Byte(EE_SLAVE_ADDRESS7);
    I2Cx_Wait_Ack();

    I2Cx_Send_Byte((uint8_t)addr);
    I2Cx_Wait_Ack();

    I2Cx_Start();
    I2Cx_Send_Byte(EE_SLAVE_ADDRESS7 | 0x01);
    I2Cx_Wait_Ack();

    data = I2Cx_Read_Byte();

    I2Cx_NAck();
    I2Cx_Stop();

    return data;
}

/**
* @brief  向24C02写数据----页写,,,最多一次写入8个字节,多了会覆盖前面的
* @param  Data--数据
* @param  StartAddress--开始的地址--最大255
* @param  None
* @retval None
* @example 
**/
bool EE_Page_Write(uint16_t addr, uint8_t *buf, uint16_t len)
{
    I2Cx_Start();
    I2Cx_Send_Byte(EE_SLAVE_ADDRESS7);
    I2Cx_Wait_Ack();

    I2Cx_Send_Byte((uint8_t)addr);
    I2Cx_Wait_Ack();

    while (len--)
    {
        I2Cx_Send_Byte(*buf++);
        I2Cx_Wait_Ack();
    }

    I2Cx_Stop();

    rt_thread_mdelay(3);

    return true;
}

/**
* @brief  从24C02读出数据----页读
* @param  Data--数据指针
* @param  StartAddress--开始的地址--最大255
* @param  None
* @retval None
* @example 
**/
bool EE_Page_Read(uint16_t addr, uint8_t *buf, uint16_t len)
{
    I2Cx_Start();
    I2Cx_Send_Byte(EE_SLAVE_ADDRESS7);
    I2Cx_Wait_Ack();

    I2Cx_Send_Byte((uint8_t)addr);
    I2Cx_Wait_Ack();

    I2Cx_Start();
    I2Cx_Send_Byte(EE_SLAVE_ADDRESS7 | 0x01);
    I2Cx_Wait_Ack();

    while (len--)
    {
        *buf = I2Cx_Read_Byte();
        buf++;
        I2Cx_Ack();
    }

    I2Cx_NAck();
    I2Cx_Stop();

    return true;
}

/*
写入数据的流程
1、判断写入数据长度，长度+起始地址<=总容量
2、确定起始地址所在页以及页内偏移量
2、长度 / I2C_PageSize，获得页面数，长度 % I2C_PageSize，获得零散字节数
3、整页部分
4、尾巴零散字节
*/
bool EE_Write_NByte(uint16_t WriteAddr, uint8_t *pBuffer, uint16_t NumByteToWrite)
{
    uint8_t NumOfPage = 0, NumOfSingle = 0, Addr = 0, count = 0;
    HAL_StatusTypeDef err = HAL_OK;

    Addr = WriteAddr % EE_PAGESIZE;
    count = EE_PAGESIZE - Addr;
    NumOfPage = NumByteToWrite / EE_PAGESIZE;
    NumOfSingle = NumByteToWrite % EE_PAGESIZE;

    /* If WriteAddr is I2C_PageSize aligned  */
    if (Addr == 0)
    {
        /* If NumByteToWrite < I2C_PageSize */
        if (NumOfPage == 0)
        {
            EE_Page_Write(WriteAddr, pBuffer, NumByteToWrite);
        }
        /* If NumByteToWrite > I2C_PageSize */
        else
        {
            while (NumOfPage--)
            {
                EE_Page_Write(WriteAddr, pBuffer, EE_PAGESIZE);
                WriteAddr += EE_PAGESIZE;
                pBuffer += EE_PAGESIZE;
            }

            if (NumOfSingle != 0)
            {
                EE_Page_Write(WriteAddr, pBuffer, NumOfSingle);
            }
        }
    }
    /* If WriteAddr is not I2C_PageSize aligned  */
    else
    {
        /* If NumByteToWrite < I2C_PageSize */
        if (NumByteToWrite <= count)
        {
            EE_Page_Write(WriteAddr, pBuffer, NumOfSingle);
        }
        /* If NumByteToWrite > I2C_PageSize */
        else
        {
            NumByteToWrite -= count;
            NumOfPage = NumByteToWrite / EE_PAGESIZE;
            NumOfSingle = NumByteToWrite % EE_PAGESIZE;

            EE_Page_Write(WriteAddr, pBuffer, count);
            WriteAddr += count;
            pBuffer += count;

            while (NumOfPage--)
            {
                EE_Page_Write(WriteAddr, pBuffer, EE_PAGESIZE);
                WriteAddr += EE_PAGESIZE;
                pBuffer += EE_PAGESIZE;
            }

            if (NumOfSingle != 0)
            {
                EE_Page_Write(WriteAddr, pBuffer, NumOfSingle);
            }
        }
    }

    return true;
}

//读出1串数据
bool EE_Read_NByte(uint16_t ReadAddr, uint8_t *pBuffer, uint16_t NumByteToRead)
{
    EE_Page_Read(ReadAddr, pBuffer, NumByteToRead);
    rt_thread_mdelay(5);

    return true;
}

// 初始化IIC的IO口
int i2c_init(void)
{
    GPIO_InitTypeDef GPIO_InitStruct; // 定义GPIO结构体

    __HAL_RCC_GPIOB_CLK_ENABLE(); // 打开GPIOB口时钟

    GPIO_InitStruct.Pin = GPIO_PIN_8 | GPIO_PIN_9;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    I2Cx_Stop();

    return RT_EOK;
}
INIT_BOARD_EXPORT(i2c_init);