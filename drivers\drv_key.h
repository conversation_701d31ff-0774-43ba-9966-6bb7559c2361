#ifndef __DRV_KEY_H__
#define __DRV_KEY_H__


#include "stm32f0xx.h"
#include "rtthread.h"

#define KEY_SERIES_FLAG  100 //按键连发开始所需时间长度
#define KEY_SERIES_DELAY 10 //按键连发的时间间隔长度
//按键属性
#define KEY_DOWN        0xA0
#define KEY_LONG        0xB0
#define KEY_LIAN        0xC0
#define KEY_UP          0xD0
#define NO_KEY          0x00
#define NO_PRESS        0xFF

#define KEY1_DOWN       0X01
#define KEY2_DOWN       0X02
#define KEY3_DOWN       0X03
#define KEY4_DOWN       0X04
#define KEY5_DOWN       0X05
#define KEY6_DOWN       0X06
#define KEY7_DOWN       0X07
#define KEY8_DOWN       0X08
#define KEY9_DOWN       0X09
#define KEY10_DOWN      0X0A
#define KEY11_DOWN      0X0B
#define KEY12_DOWN      0X0C
#define KEY13_DOWN      0X0D
#define KEY14_DOWN      0x0E
#define KEY15_DOWN      0x0F

#define KEY1_SDOWN      0XA1
#define KEY2_SDOWN      0XA2
#define KEY3_SDOWN      0XA3
#define KEY4_SDOWN      0XA4
#define KEY5_SDOWN      0XA5
#define KEY6_SDOWN      0XA6
#define KEY7_SDOWN      0XA7
#define KEY8_SDOWN      0XA8
#define KEY9_SDOWN      0XA9
#define KEY10_SDOWN     0XAA
#define KEY11_SDOWN     0XAB
#define KEY12_SDOWN     0XAC
#define KEY13_SDOWN     0XAD
#define KEY14_SDOWN     0XAE
#define KEY15_SDOWN     0xAF

#define KEY1_REALESE    0XD1
#define KEY2_REALESE    0XD2
#define KEY3_REALESE    0XD3
#define KEY4_REALESE    0XD4
#define KEY5_REALESE    0XD5
#define KEY6_REALESE    0XD6
#define KEY7_REALESE    0XD7
#define KEY8_REALESE    0XD8
#define KEY9_REALESE    0XD9
#define KEY10_REALESE   0XDA
#define KEY11_REALESE   0XDB
#define KEY12_REALESE   0XDC
#define KEY13_REALESE   0XDD
#define KEY14_REALESE   0XDE

#define KEY1    (GPIOB->IDR & GPIO_PIN_6)
#define KEY2    (GPIOB->IDR & GPIO_PIN_5)
#define KEY3    (GPIOB->IDR & GPIO_PIN_4)
#define KEY4    (GPIOB->IDR & GPIO_PIN_3)
#define KEY5    (GPIOD->IDR & GPIO_PIN_2)
#define KEY6    (GPIOC->IDR & GPIO_PIN_12)
#define KEY7    (GPIOC->IDR & GPIO_PIN_11)
#define KEY8    (GPIOC->IDR & GPIO_PIN_10)
#define KEY9    (GPIOA->IDR & GPIO_PIN_15)
#define KEY10   (GPIOF->IDR & GPIO_PIN_7)
#define KEY11   (GPIOF->IDR & GPIO_PIN_6)
#define KEY12   (GPIOA->IDR & GPIO_PIN_12)
#define KEY13   (GPIOA->IDR & GPIO_PIN_11)

#define COIN    (GPIOC->IDR & GPIO_PIN_2)   


extern uint8_t Key_Scan(void);

#endif
