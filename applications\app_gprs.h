#ifndef __APP_GPRS_H__
#define __APP_GPRS_H__

#include "stm32f0xx.h"
#include "rtthread.h"

#include "drv_uart.h"

extern rt_sem_t gprs_recv_sem;
//extern struct rt_semaphore gprs_recv_sem;

extern void get_gprs_data_timer_out(void);
extern void update_gprs_data(UART_HandleTypeDef *huart);

extern uint16_t get_remote_port_state(uint8_t x);
extern void set_remote_port_state(uint8_t x, uint16_t temp);

extern void create_gprs_send_dynamic_thread(void);
extern void delete_gprs_send_dynamic_thread(void);

#endif
