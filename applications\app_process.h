#ifndef __APP_PROCESS_H__
#define __APP_PROCESS_H__

#include "stdbool.h"

#include "stm32f0xx.h"
#include "rtthread.h"
#include "drv_buzzer.h"
#include "drv_tm1640.h"
#include "drv_relay.h"
#include "drv_uart.h"
#include "drv_eeprom.h"
#include "drv_iwdg.h"

#include "filter.h"

#define VERSION 25101

#define WATCH_DOG

#define TOTAL_PORTS_NUM     2
#define START_ITEM_NUM      1

#define READ    true
#define WRITE   false
    
#define COIN_ENABLE     (GPIOC->BSRR = (uint32_t)GPIO_PIN_13)
#define COIN_DISABLE    (GPIOC->BRR = (uint32_t)GPIO_PIN_13)

typedef enum{
    IDLE_STA = 0,   //无连接状态
    LOCK_STA,       //锁定状态
    HIGH_STA,       //功率过高状态
	NOMAL_STA,       //正常充电状态
    ERROR_STA,
//    CHECK_POWER_STA //初始检测功率状态
}ChargeState;

typedef enum{
	NoState = 0,
	NomalChargeState,
//	SetState,
	ClearState,
	CalibrateState,
//	SwipeState,
//	CoinState,
	TestState,
//	NoConnectState,
//	HighPowerState
}WorkState;

typedef enum{
	NO_TYPE = 0,
	COIN_TYPE,
	LOCAL_SWIPE_TYPE,
    ONLINE_SWIPE_TYPE,
    REMOTE_TYPE
}Consume;

typedef enum{
    TIME_DONE_STOP = 0,
    POWER_ENERGY_DONE_STOP = 0,
    MANUAL_STOP,
    AUTO_STOP,
    OVER_POWER_STOP,
    REMOTE_STOP,
    FAULT_STOP = 0x0B,
}StopReason;

//typedef struct Ic_card_t * pcard;
//typedef struct WorkState_t * pworkstate[10];

struct Charge_t{
    uint32_t card_serialNum;
    uint16_t chargeTimes;
    uint16_t chargePower;	//充电功率
    uint16_t remainBattery;	//剩余电量
    uint16_t remainAmount;	//剩余金额
    
    uint16_t totalChargeTime;
    uint16_t totalEnergyConsumption;
    uint16_t totalAmount;
    
    uint16_t lastChargeTime;
    uint16_t lastTotalChargeTime;
//    uint16_t last_remain_energy;
    uint16_t last_remain_amount;
//    uint16_t lastTotalEnergyConsumption;
//    uint16_t lastAmount;
    
//    uint16_t reserveCoinCounts;
//    uint16_t reserveSwipeTimes;
    uint16_t last_card_amount;
    uint16_t error_cnt;
	
    uint8_t minites;
    uint8_t check_cnt;
    uint8_t back_cnt;
    
    uint8_t payType;
    uint8_t state;
    
    uint8_t isCharge:1;
    uint8_t isFloatCharge:1;
    uint8_t isNoConnect:1;
    uint8_t isHighPower:1;
    uint8_t isReconnect:1;
    uint8_t isRecheckPower:1;
    uint8_t manual_recovery_amount_flag:1;
	uint8_t :1;
//    uint8_t en_disp:1;
};

struct WorkState_t{
    struct Charge_t charge;
    
    uint8_t work;
    uint8_t isAjustState:1;
	uint8_t :1;
	uint8_t :1;
	uint8_t :1;
	uint8_t :1;
	uint8_t :1;
	uint8_t :1;
	uint8_t :1;
};

extern struct rt_semaphore detect_sem;
extern struct rt_mailbox gprs_transmit_mb;

extern void save_remote_sys_parameters(uint16_t *ptr);
extern void main_view(struct disp_t *pdisp, uint8_t i);
extern void stop_charge_port(uint8_t x);
extern void init_sys_parameters(uint8_t x, uint16_t temp);
extern uint16_t rw_sys_parameters(uint8_t sta, uint8_t x, uint16_t temp);
extern void send_gprs_mb_cmd(uint8_t cmd, uint8_t port);

extern Consume get_last_paytype(void);
extern ChargeState get_charge_disp_state(uint8_t x);
extern Consume get_charge_paytype(uint8_t x);
extern StopReason get_charge_port_stop_reason(void);

extern uint8_t get_last_charge_port(void);
extern uint8_t get_manual_recovery_amount_flag(uint8_t x);
extern uint8_t get_billing_method(void);
extern uint16_t get_limit_total_charge_power(void);
extern uint8_t get_charge_check_cnt(uint8_t x);
extern uint8_t get_limited_temp(void);
extern uint8_t get_freeCharge_sta(void);
extern uint16_t get_port_lock_state(void);
extern uint8_t get_error_sta(void);
extern uint16_t get_port_error_state(void);
extern uint16_t get_last_charge_amount(void);
extern uint8_t get_rechoose_state(void);
extern uint8_t get_floatcharge_state(uint8_t x);
extern uint8_t get_allowrecovery_state(void);
extern uint8_t get_charge_noconnect_state(uint8_t x);
extern uint8_t get_error_highpower_state(uint8_t x);
extern uint8_t get_selfstop_state(void);
extern uint8_t get_charge_state(uint8_t x);
extern uint8_t get_test_state(uint8_t x);
extern uint8_t get_calibrate_state(uint8_t x);
extern uint8_t get_charge_recheck_state(uint8_t x);
extern uint8_t get_charge_reconnect_state(uint8_t x);

extern uint16_t get_port_card_last_amount(uint8_t x);
extern uint32_t get_port_card_serilanum(uint8_t x);
extern uint16_t get_increased_coins(void);
extern uint16_t get_increased_card_amount(void);
extern uint16_t get_increased_charge_time(void);
extern uint16_t get_increased_electricity(void);
extern uint16_t get_charge_coin_paytype_state(void);
extern uint16_t get_charge_local_card_paytype_state(void);
extern uint16_t get_charge_online_card_paytype_state(void);
extern uint16_t get_charge_remote_paytype_state(void);
extern uint16_t get_before_stop_remain_time(void);
extern uint16_t get_before_stop_remain_battery(void);
extern uint16_t get_charge_enable_state(void);
extern uint16_t get_charge_power_value(uint8_t x);
extern uint16_t get_coin_totalAmount(void);
extern uint16_t get_level_percentage(uint8_t level);
extern uint16_t get_level_max_power(uint8_t level);
extern uint16_t get_last_total_charge_time(uint8_t x);
extern uint16_t get_last_charge_time(uint8_t x);
extern uint16_t get_charge_total_time(uint8_t x);
extern uint16_t get_charge_total_amount(uint8_t x);
extern uint16_t get_charge_remain_amount(uint8_t x);
extern uint16_t get_charge_remain_battery(uint8_t x);
extern uint16_t get_charge_total_energies(uint8_t x);
extern uint16_t get_charge_times(uint8_t x);
extern uint16_t get_float_charge_time(void);
extern uint16_t get_max_float_charge_value(void);
extern uint16_t get_limited_power_value(void);
extern uint16_t get_calibrate_ref_over_value(void);
extern uint16_t get_calibrate_ref_energy_value(uint8_t x);
extern uint16_t get_calibrate_ref_freq_value(uint8_t x);
extern uint16_t get_calibrate_ref_power_value(void);
extern uint16_t get_ic_card_charge_time_value(void);
extern uint16_t get_ic_card_deduction_amount(void);
extern uint16_t get_detect_timeout_time(void);

extern void set_manual_recovery_amount_flag(uint8_t x, uint8_t temp);
extern void set_upload_all_timer(uint16_t temp);
extern void set_error_sta(uint8_t sta);
extern void set_coin_coins(uint8_t temp);
extern void set_rechoose_state(uint8_t sta);
extern void set_coin_enable_state(uint8_t sta);
extern void set_last_paytype(Consume type);
extern void set_stop_disp_flag(uint8_t sta);
extern void set_port_error_state(uint8_t sta, uint8_t x);
extern void set_last_charge_amount(uint16_t x);
extern void set_last_charge_port(uint8_t x);
extern void set_before_stop_remain_time(uint16_t temp);
extern void set_before_stop_remain_battery(uint16_t temp);
extern void set_charge_port_stop_reason(StopReason reason);
extern void set_port_lock_state(uint8_t sta, uint16_t temp);
extern void set_charge_remote_paytype_state(uint8_t sta, uint16_t temp);
extern void set_charge_local_card_paytype_state(uint8_t sta, uint16_t temp);
extern void set_charge_online_card_paytype_state(uint8_t sta, uint16_t temp);
extern void set_charge_coin_paytype_state(uint8_t sta, uint16_t temp);
extern void set_charge_port_enable_state(uint8_t sta, uint16_t temp);
extern void set_charge_paytype(uint8_t x, Consume sta);
extern void set_charge_work_state(uint8_t x, WorkState sta);
extern void set_charge_enable_flag(uint8_t x, uint8_t sta);
extern void set_charge_state(uint8_t x, ChargeState sta);
extern void set_charge_reset(uint8_t x);
extern void set_back_timer_value(uint8_t temp);
extern void set_ref_energy_value(uint8_t x, uint16_t temp);
extern void set_calibrate_complete_state(uint8_t sta);
extern void set_calibrate_ref_over_value(uint16_t temp);
extern void set_charge_power_value(uint8_t x, uint16_t temp);
extern void set_error_cnt(uint8_t x, uint8_t temp);
extern void set_charge_recheck_state(uint8_t x, uint8_t sta);
extern void set_charge_reconnect_state(uint8_t x, uint8_t sta);
extern void set_charge_noconnect_state(uint8_t x, uint8_t sta);
extern void set_error_highpower_state(uint8_t x, uint8_t sta);
extern void set_floatcharge_state(uint8_t x, uint8_t sta);
extern void set_charge_minites(uint8_t x, uint8_t temp);
extern void set_charge_times(uint8_t x, uint16_t temp);
extern void set_charge_total_time(uint8_t x, uint16_t temp);
extern void set_last_charge_time(uint8_t x, uint16_t temp);
extern void set_total_charge_time(uint8_t x, uint16_t temp);
extern void set_charge_total_battery(uint8_t x, uint16_t temp);
extern void set_charge_total_amount(uint8_t x, uint16_t temp);
extern void set_last_total_charge_time(uint8_t x, uint16_t temp);
extern void set_charge_remain_amount(uint8_t x, uint16_t temp);
extern void set_charge_remain_battery(uint8_t x, uint16_t temp);
extern void set_charge_check_cnt(uint8_t x, uint8_t temp);
extern void set_test_times(uint8_t temp);
extern void set_test_state(uint8_t x, uint8_t sta);
extern void set_test_connected_state(uint8_t x, uint8_t sta);
extern void set_calibrate_ref_freq_parameters(uint8_t x, uint16_t temp);
extern void set_calibrate_ref_power_parameters(uint16_t temp);
extern void set_calibrate_ref_energy_parameters(uint8_t x, uint16_t temp);

#endif
