#include "app_viewpoll.h"
#include "app_process.h"

#define THREAD_PRIORITY 15
#define THREAD_TIMESLICE 5

ALIGN(RT_ALIGN_SIZE)
static char viewpoll_stack[512];
static struct rt_thread viewpoll;

//主界面
static void show_main(void)
{
    uint8_t i = 0;
    tm1640_disp_t disp;

    for (i = 0; i < 10; i++)
    {
        switch (workStates[i].charge.state)
        {
        case IDLE_STA:
            disp.type = Idle;
            disp.data = i + 1;
            break;
        case ERROR_STA:
            disp.type = Error;
            disp.data = DISP_ERROR_NUM;
            break;
        case HIGH_STA:
            disp.type = Error;
            disp.data = DISP_HIGH_NUM;
            break;
        case NOMAL_STA:
            if ((workStates[i].test.isTest == true && workStates[i].test.time != 0) || workStates[i].test.isConnect == true)
            {
                workStates[i].work = TestState;
                workStates[i].charge.state = NOMAL_STA;
                disp.type = Idle;
                disp.data = i + 1;
            }
            else
            {
                disp.type = Parameters;
                disp.data = 100;
            }
            break;
        default:
            break;
        }

        disp.addr = i;
        disp.point = No_Point;
        disp.hide = No_Hide;

        tm1640.disp(disp);
    }
}

//测试界面
static void view_test(uint8_t x)
{
    //    tm1640_disp_t disp;
    //
    //    if(action.flip != 0)
    //    {
    //        disp.hide = Hidden_All;
    //    }
    //    else
    //    {
    //        disp.hide = No_Hide;
    //    }
    //
    //    if(workStates[x].test.isConnect == true)
    //    {
    //        if(++action.testFlashCount >= 20)
    //        {
    //            action.testFlashCount = 0;
    //            disp.hide = No_Hide;
    //            workStates[x].work = NoState;
    //            workStates[x].charge.state = IDLE_STA;
    //            workStates[x].test.isTest = false;
    //            workStates[x].test.isConnect = false;
    //        }
    //    }
    //    else if(workStates[x].test.isTest == true)
    //    {
    //
    //        if(--action.testTimes == 0)
    //        {
    //            disp.hide = No_Hide;
    //            action.last_test_port = 0;
    //            workStates[x].work = NoState;
    //            workStates[x].charge.state = IDLE_STA;
    //            workStates[x].test.isTest = false;
    //            relay.write(x, GPIO_PIN_RESET);
    //        }
    //    }

    //    if(set.isSet == false && calibrate.isCalibrate == false && clear.isClear == false\
//        && card.isSwipe == false && coin.isCoin == false)
    //    {
    //        disp.type = Idle;
    //        disp.addr = x;
    //        disp.data = x+1;
    //        disp.point = No_Point;
    //        tm1640.disp(disp);
    //    }
}

//充电清零界面
static void view_clear(void)
{
    //	uint8_t i,j;
    //    tm1640_disp_t disp;
    //    workState state;
    //
    //	for(i = 0;i < 10;i ++)
    //	{
    //        j = workStates[i].charge.state;
    //        switch(j)
    //        {
    //            case ERROR_STA:
    //                disp.type = Error;
    //                disp.data = DISP_ERROR_NUM;
    //                state = NoState;
    //                break;
    //            case HIGH_STA:
    //                disp.type = Error;
    //                disp.data = DISP_HIGH_NUM;
    //                state = NoState;
    //                break;
    //            case NOMAL_STA:
    //                disp.type = Parameters;
    //                state = ClearState;
    //                if(workStates[i].test.isTest == true)
    //                {
    //                    disp.data = action.testTimes;
    //                }
    //                else if(workStates[i].charge.isCharge == true)
    //                {
    //                    disp.data = workStates[i].charge.chargeTimes;
    //                }
    //                else if(workStates[i].test.isConnect == true)
    //                {
    //                    disp.type = Error;
    //                    disp.data = DISP_LINE_NUM;
    //                    state = NoState;
    //                }
    //                break;
    //            default:
    //                disp.type = Error;
    //                disp.data = DISP_LINE_NUM;
    //                state = NoState;
    //                break;
    //        }
    //		workStates[i].work = state;
    //
    //        disp.addr = i;
    //        disp.point = No_Point;
    //        disp.hide = No_Hide;
    //		tm1640.disp(disp);
    //	}
}

//后台计时
static void backstage_timing(uint8_t x)
{
    tm1640_disp_t disp;

    //	if(workStates[x].test.isTest == true)
    //	{
    //		if(--action.testTimes == 0)
    //		{
    //			disp.type = Error;
    //			disp.data = DISP_LINE_NUM;
    //			workStates[x].work = NoState;
    //			workStates[x].charge.state = IDLE_STA;
    //			workStates[x].test.isTest = false;
    //			action.last_test_port = 0;
    //			relay.write(x, GPIO_PIN_RESET);
    //		}
    //		else
    //		{
    //			disp.type = Parameters;
    //			disp.data = action.testTimes;
    //		}
    //        disp.addr = x;
    //        disp.point = No_Point;
    //        disp.hide = No_Hide;
    //		tm1640.disp(disp);
    //	}
    //	else if(workStates[x].charge.isCharge == true)
    //	{
    //		if(++workStates[x].charge.min >= 60)
    //		{
    //			workStates[x].charge.min = 0;
    //			if(--workStates[x].charge.chargeTimes == 0)
    //			{
    //				workStates[x].charge.min = 0;
    //				workStates[x].work = NoState;
    //				workStates[x].charge.state = IDLE_STA;
    //				workStates[x].charge.isCharge = false;
    //				workStates[x].charge.isFloatCharge = false;
    ////				action.data.status &= ~byte[port];
    //				relay.write(x, GPIO_PIN_RESET);
    ////				hlw8012.init_rom(port,D_NORMAL_MODE);
    ////				action.reset_port(port);
    //                disp.type = Idle;
    //                disp.data = 0;
    ////				portData[port].consumption.payType = NO_TYPE;
    ////				portData[port].charge.totalChargeTime = 0;
    ////				portData[port].charge.totalEnergyConsumption = 0;
    ////				portData[port].charge.totalAmount = 0;
    ////				portData[port].charge.chargeTime = 0;
    ////				portData[port].charge.chargePower = 0;
    ////				portData[port].charge.remainBattery = 0;
    ////				portData[port].charge.remainAmount = 0;
    ////				portData[port].charge.lastMaxChargePower = 0;
    ////				portData[port].charge.lastTotalChargeTime = 0;
    //			}
    //			else
    //			{
    //				disp.type = Parameters;
    //				disp.data = workStates[x].charge.chargeTimes;
    //			}
    //            disp.addr = x;
    //            disp.point = No_Point;
    //            disp.hide = No_Hide;
    //			tm1640.disp(disp);
    //		}
    //	}
}

//校准界面
static void view_calibrate(uint8_t x)
{
    uint16_t dispdata;
    uint32_t t;
    static bool dir = false;
    static uint8_t pointpos = 0;

    //	if(action.statusFlag.calibrate.calibrateCompleteFlag == true)
    //	{
    //		dir = 0;
    //		pointpos = 0;
    //		action.statusFlag.calibrate.calibrateCompleteFlag = false;
    //		action.statusFlag.calibrate.calibrating = false;
    //		workStatusFlag[port_num].calibrate.isCalibrateFlag = false;
    //		workStatusFlag[port_num].workState = NoState;
    //
    //		if (hlw8012_parameters[port_num].B_P_Last_TestOneCycle_Mode == 1)
    //		{
    //			//单周期测量模式
    //			t = hlw8012_parameters[port_num].U16_P_Last_OneCycleTime;
    //			t *= 1000; //ms转换成us
    //		}
    //		else
    //		{
    //			//计数脉冲测量模式
    //			t = hlw8012_parameters[port_num].U16_P_Last_OneCycleTime;
    //			t *= 1000;
    //			t /= (hlw8012_parameters[port_num].U16_P_Last_CNT - 1);
    //		}
    //		//校准结束，保存得到的参考频率
    //		if(t > 65535)
    //		{
    //			action.data.calibrate.overFlag |= byte[port_num];
    //			action.data.calibrate.refFreq[port_num] = t - 65535;
    //		}
    //		else
    //		{
    //			action.data.calibrate.overFlag &= ~byte[port_num];
    //			action.data.calibrate.refFreq[port_num] = (uint16_t)t;
    //		}
    //		relay.control(port_num,false);
    //		hlw8012.init_rom(port_num,D_NORMAL_MODE);
    ////		messageQueue.post(MessageEeprom,REF_PARA);
    //		if(action.data.calibrate.refFreq[port_num] > 10000)
    //			dispdata = action.data.calibrate.refFreq[port_num] / 100;
    //		else
    //			dispdata = action.data.calibrate.refFreq[port_num] / 10;
    //		ht1640.disp(Disp_Type_Parameters,port_num,dispdata,No_Point,Disp_All);
    //	}
    //	else
    //	{
    //		ht1640.disp(Disp_Type_Parameters,port_num,0,pointpos,Hidden_All);
    //		if(dir == false)
    //		{
    //			if(++pointpos >= 2)
    //				dir = true;
    //		}
    //		else
    //		{
    //			if(--pointpos == 0)
    //				dir = false;
    //		}
    //	}
}

//轮询处理显示状态
static void rotary_display(void)
{
    //    uint8_t i;
    //    uint8_t state;
    //
    //    action.flip = ~action.flip;
    //
    //    for(i = 0; i < 10; i ++)
    //    {
    //        state = workStates[i].work;
    //        switch(state)
    //        {
    //            case NomalChargeState:
    //                break;
    //            case ClearState:
    //                backstage_timing(i);
    //                break;
    //            case TestState:
    //                view_test(i);
    //                break;
    //            default:
    //                break;
    //        }
    //    }
}

static void viewpoll_entry(void *arg)
{
    show_main();
    speek.cmd(Voice_Commd_Welcome);

    while (1)
    {
        rotary_display();
        rt_thread_delay(1000);
    }
}

int viewpoll_thread(void)
{
    rt_thread_init(&viewpoll,
                   "viewpoll",
                   viewpoll_entry,
                   RT_NULL,
                   &viewpoll_stack[0],
                   sizeof(viewpoll_stack),
                   THREAD_PRIORITY,
                   THREAD_TIMESLICE);
    rt_thread_startup(&viewpoll);

    return RT_EOK;
}
INIT_APP_EXPORT(viewpoll_thread);

view_t view = {
    show_main};
