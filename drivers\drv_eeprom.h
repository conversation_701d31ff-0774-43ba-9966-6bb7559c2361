#ifndef __DRV_EEPROM_H__
#define __DRV_EEPROM_H__

#include "stm32f0xx.h"
#include "rtthread.h"

extern HAL_StatusTypeDef AT24CXX_ReadByte(uint8_t addr, uint8_t Value);
extern HAL_StatusTypeDef At24c02_Write_Byte(uint16_t addr, uint8_t* dat);
extern HAL_StatusTypeDef AT24CXX_PageWrite(uint16_t MemAddress, uint8_t* pBuffer, uint32_t BufferSize);
extern HAL_StatusTypeDef At24c02_Write_NByte(uint16_t addr, uint8_t* dat, uint16_t size);
extern HAL_StatusTypeDef At24c02_Read_NByte(uint16_t addr, uint8_t* recv_buf, uint16_t size);

#endif
