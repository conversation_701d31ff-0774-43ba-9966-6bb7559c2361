#ifndef __DRV_24CXX_H__
#define __DRV_24CXX_H__

#include "drv_soft_i2c.h"

#define AT24C01 0
#define AT24C02 1
#define AT24C04 2
#define AT24C08 3
#define AT24C16 4
#define AT24C32 5
#define AT24C64 6
#define AT24C128 7
#define AT24C256 8
#define AT24C512 9
#define AT24CTYPE 10 // Number of supported types

#define EE_TWR 5

#ifndef EE_TYPE
#define EE_TYPE AT24C08
#endif

struct at24cxx_device
{
    struct rt_i2c_bus_device *i2c;
    rt_mutex_t lock;
};
typedef struct at24cxx_device *at24cxx_device_t;

extern rt_err_t at24cxx_check(at24cxx_device_t dev, uint8_t check_code);
extern at24cxx_device_t at24cxx_init(void);
extern void at24cxx_deinit(at24cxx_device_t dev);
extern rt_err_t at24cxx_read(at24cxx_device_t dev, uint32_t ReadAddr, uint8_t *pBuffer, uint16_t NumToRead);
extern rt_err_t at24cxx_write(at24cxx_device_t dev, uint32_t WriteAddr, uint8_t *pBuffer, uint16_t NumToWrite);
extern rt_err_t at24cxx_page_read(at24cxx_device_t dev, uint32_t ReadAddr, uint8_t *pBuffer, uint16_t NumToRead);
extern rt_err_t at24cxx_page_write(at24cxx_device_t dev, uint32_t WriteAddr, uint8_t *pBuffer, uint16_t NumToWrite);

#endif // !__DRV_24C02_H__
