/*
 * Copyright (c) 2006-2020, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2020-08-31     RT-Thread    first version
 */

#include <rtthread.h>
#include "app_process.h"
#include "app_voice.h"

#include "drv_buzzer.h"
#include "drv_hlw8012.h"

//初始化显示界面
int sys_is_ready(void)
{
    //	t_voice_data voice_data;
    struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);

	for(uint8_t i = 0; i < TOTAL_PORTS_NUM; i ++)
	{
		main_view(pdisp, i);
	}

    rt_free_align(pdisp);

    speeker(Voice_Commd_Welcome);
    //	voice_data.type = Voice_Commd_Welcome;
    //	voice_data.power = 0;
    //	voice_data.time = 0;
    //	voice_data.balance = 0;
    //	spell_voice(&voice_data);

    for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
    {
        init_charge_rom(i, D_NORMAL_MODE);
        reset_filter_data(i);
    }

    return RT_EOK;
}
INIT_ENV_EXPORT(sys_is_ready);

int main(void)
{
    //    sys_is_ready();
    return RT_EOK;
}

//硬件定时器中断回调函数
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM1)
    {
        hlw8012_update_timer_count();
        get_measure_power_value();
    }
}

//硬件外部中断回调函数
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    hlw8012_update_exti_state(GPIO_Pin);
}
