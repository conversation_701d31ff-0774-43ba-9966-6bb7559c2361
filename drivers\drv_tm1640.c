#include "drv_tm1640.h"
#include "app_process.h"
#include "app_powerdetect.h"
/* 
控制显示 
0x88,   0x89,  0x8a,  0x8b,  0x8c,  0x8d,  0x8e,  0x8f 分别对应 
1/16,  2/16,  4/16,  10/16, 11/16, 12/16, 13/16, 14/16   
*/
#define WRITE_DATA_MODE_CONTINUOUS 0x40 // 地址自动加
#define WRITE_DATA_MODE_FIXED 0x44      // 固定地址
#define START_DATA 0xC0                 // 起始地址
#define DISPLAY_EN 0x8A                 // 开显示
#define DISPLAY_DIS 0x80                // 关显示

#define CLEAR_ALL 11

static const uint8_t segTab[17] = {0xbe, 0xa0, 0x6e, 0xea, 0xf0, 0xda, 0xde, 0xa8, 0xfe, 0xfa, 0x40, 0x00, 0x5e, 0xf4, 0x08, 0x02, 0x1e};
								/*  0     1     2     3     4     5     6      7     8     9     -   不显   E     H    */
static const uint8_t circle_tab[13][3] = {{0x10, 0x00, 0x00},
                                          {0x18, 0x00, 0x00},
                                          {0x18, 0x08, 0x00},
                                          {0x18, 0x08, 0x08},
                                          {0x18, 0x08, 0x28},
                                          {0x18, 0x08, 0xa8},
                                          {0x18, 0x08, 0xaa},
                                          {0x18, 0x0a, 0xaa},
                                          {0x1a, 0x0a, 0xaa},
                                          {0x1e, 0x0a, 0xaa},
                                          {0x5e, 0x0a, 0xaa},
                                          {0x5e, 0x4a, 0xaa},
                                          {0x5e, 0x4a, 0xea}};

static const uint8_t dot_tab[3][3] = {{0xc6, 0x00, 0x00},
                                      {0x00, 0xc6, 0x00},
                                      {0x00, 0x00, 0xc6}};

static const uint8_t chip_id[TOTAL_PORTS_NUM][2] = {
    {2, 3},
    {1, 0},
//    {1, 6},
//    {2, 6},
//    {2, 0},
//    {1, 3},
};

static void tm1640_start(uint8_t chip)
{
    TM1640_DAT_HIGH(chip);
    rt_hw_us_delay(10);
    TM1640_CLK_HIGH(chip);
    rt_hw_us_delay(10);
    TM1640_DAT_LOW(chip);
    rt_hw_us_delay(10);
    TM1640_CLK_LOW(chip);
}

static void tm1640_stop(uint8_t chip)
{
    TM1640_CLK_LOW(chip);
    TM1640_DAT_LOW(chip);
    rt_hw_us_delay(10);
    TM1640_CLK_HIGH(chip);
    TM1640_DAT_HIGH(chip);
}

static void tm1640_send_byte(uint8_t chip, uint8_t c)
{
    uint8_t i = 0;

    for (i = 0; i < 8; i++)
    {
        TM1640_CLK_LOW(chip);
        if (c & 0x01)
            TM1640_DAT_HIGH(chip);
        else
            TM1640_DAT_LOW(chip);
        rt_hw_us_delay(10);
        TM1640_CLK_HIGH(chip);
		rt_hw_us_delay(10);
        c >>= 1;
    }
    TM1640_CLK_LOW(chip);
    TM1640_DAT_LOW(chip);
}

static void tm1640_send_str(uint8_t chip, uint8_t addr, uint8_t *p, uint8_t len, point_type point, hide_type hide)
{
    uint8_t i = 0;
    uint8_t data = 0;

    tm1640_start(chip);
    tm1640_send_byte(chip, WRITE_DATA_MODE_CONTINUOUS);
    tm1640_stop(chip);

    tm1640_start(chip);
    tm1640_send_byte(chip, START_DATA + addr);

    for (i = 0; i < len; i++)
    {
        if (i == point)
            data = segTab[p[i]] | 0x01;
        else
            data = segTab[p[i]];

        if (i == hide)
        {
            data = 0;
        }
        else if (Hidden_Pos_2and3 == hide)
        {
            if (i == 1 || i == 2)
                data = 0;
        }
        else if (Hidden_All == hide)
        {
            if (point != No_Point)
            {
                if (i == point)
                {
                    data = 0;
                    data |= 0x01;
                }
                else
                {
                    data = 0;
                }
            }
            else
            {
                data = 0;
            }
        }
        tm1640_send_byte(chip, data);
    }

    tm1640_stop(chip);
}

void seg_clear(void)
{
    uint8_t i = 0;
    uint8_t data[15];

    for (i = 0; i < 15; i++)
        data[i] = CLEAR_ALL;

    tm1640_send_str(1, 0, data, 15, No_Point, No_Hide);
    tm1640_send_str(2, 0, data, 15, No_Point, No_Hide);
}

void auto_show_digitaltube(struct disp_t *pdisp)
{
    uint8_t chip = 0;
    uint8_t addr = 0;
    uint8_t len = 0;
    uint8_t tab[6];

    if (pdisp->addr < 5)
    {
        chip = 1;
        addr = pdisp->addr * 3;
    }
    else
    {
        chip = 2;
        addr = (pdisp->addr - 5) * 3;
    }

    //    chip = chip_id[pdisp->addr][0];
    //    addr = chip_id[pdisp->addr][1];

    switch (pdisp->type)
    {
    case Idle:
        len = 3;
        if (pdisp->data == 10)
            tab[2] = pdisp->data / 10;
        else
            tab[2] = 10;
        tab[1] = pdisp->data % 10;
        tab[0] = 10;
        break;
    case Error:
        len = 3;
        tab[2] = 10;
        tab[1] = pdisp->data;
        tab[0] = 10;
        break;
    case Set:
        len = 3;
        tab[2] = pdisp->data / 10;
        tab[1] = 10;
        tab[0] = pdisp->data % 10;
        break;
    case Check_Amount:
        len = 6;
        //            tab[6] = pdisp->data % 1000 / 100;
        //            tab[7] = pdisp->data % 100 / 10;
        //            tab[8] = pdisp->data % 10;
        //            tab[3] = 11;
        //            tab[4] = 11;
        //            tab[5] = 11;
        //            tab[0] = pdisp->data / 100000;
        //            tab[1] = pdisp->data % 100000 / 10000;
        //            tab[2] = pdisp->data % 10000 / 1000;
        tab[5] = pdisp->data % 1000 / 100;
        tab[4] = pdisp->data % 100 / 10;
        tab[3] = pdisp->data % 10;
        tab[2] = pdisp->data / 100000;
        tab[1] = pdisp->data % 100000 / 10000;
        tab[0] = pdisp->data % 10000 / 1000;
        break;
    case Check_power:
        len = 3;
        tab[2] = 14;
        tab[1] = 15;
        tab[0] = 14;
        break;
    case Check_temperture:
        len = 3;
        if (get_temp_symbol() == 0)
        {
            tab[2] = pdisp->data / 100;
            tab[1] = pdisp->data % 100 / 10;
            tab[0] = pdisp->data % 10;
        }
        else
        {
            tab[2] = 10;
            tab[1] = pdisp->data / 10;
            tab[0] = pdisp->data % 10;
        }
        break;
    case Check_Version:
        len = 6;
        tab[5] = pdisp->data % 1000 / 100;
        tab[4] = pdisp->data % 100 / 10;
        tab[3] = pdisp->data % 10;
        tab[2] = 16;
        tab[1] = pdisp->data / 10000;
        tab[0] = pdisp->data % 10000 / 1000;
        break;
    default:
        len = 3;
        tab[2] = pdisp->data / 100;
        tab[1] = pdisp->data % 100 / 10;
        tab[0] = pdisp->data % 10;
        break;
    }
    tm1640_send_str(chip, addr, tab, len, pdisp->point, pdisp->hide);
}

//转圈圈显示
void show_circle(uint8_t group_num, uint8_t addr)
{
    uint8_t i = 0, chip = 0, seg_num, tab[3];

    if (group_num < 5)
    {
        chip = 1;
        seg_num = group_num * 3;
    }
    else
    {
        chip = 2;
        seg_num = (group_num - 5) * 3;
    }

    //    chip = chip_id[group_num][0];
    //    seg_num = chip_id[group_num][1];

    tab[2] = circle_tab[addr][0];
    tab[1] = circle_tab[addr][1];
    tab[0] = circle_tab[addr][2];

    tm1640_start(chip);
    tm1640_send_byte(chip, WRITE_DATA_MODE_CONTINUOUS);
    tm1640_stop(chip);

    tm1640_start(chip);
    tm1640_send_byte(chip, START_DATA + seg_num);

    for (i = 0; i < 3; i++)
        tm1640_send_byte(chip, tab[i]);

    tm1640_stop(chip);
}

void show_wait_dot(uint8_t pos_addr)
{
    uint8_t i = 0, tab[3];

    tab[2] = dot_tab[pos_addr][0];
    tab[1] = dot_tab[pos_addr][1];
    tab[0] = dot_tab[pos_addr][2];

    tm1640_start(1);
    tm1640_send_byte(1, WRITE_DATA_MODE_CONTINUOUS);
    tm1640_stop(1);

    tm1640_start(1);
    tm1640_send_byte(1, START_DATA);

    for (i = 0; i < 3; i++)
        tm1640_send_byte(1, tab[i]);

    tm1640_stop(1);
}

int tm1640_init(void)
{

    TM1640_CLK_HIGH(1);
    TM1640_DAT_HIGH(1);
    TM1640_CLK_HIGH(2);
    TM1640_DAT_HIGH(2);

    tm1640_start(1);
    tm1640_send_byte(1, DISPLAY_EN);
    tm1640_stop(1);

    tm1640_start(2);
    tm1640_send_byte(2, DISPLAY_EN);
    tm1640_stop(2);

    //    seg_clear();

    return RT_EOK;
}
INIT_BOARD_EXPORT(tm1640_init);
