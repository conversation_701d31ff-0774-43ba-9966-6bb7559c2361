#include "app_process.h"
#include "app_keypoll.h"
#include "app_smartcard.h"
#include "app_powerdetect.h"
#include "app_data.h"
#include "app_gprs.h"
#include "app_voice.h"

// 系统设置参数
struct Set_t
{
    uint8_t isSet : 1;
    uint8_t allowedRecovery : 1;
    uint8_t billing_method : 1;
    uint8_t freecharge : 1;
    uint8_t allowedSelfStop : 1;
    uint8_t initDisplay : 1;
    uint8_t : 1;
    uint8_t : 1;

    uint8_t coinMaxPowerConsumption;
    uint8_t cardMaxPowerConsumption;
    uint8_t cardDeductionAmount;

    uint8_t level2ChargePercentage;
    uint8_t level3ChargePercentage;
    uint8_t level4ChargePercentage;

    uint8_t maxFloatPower;
    uint8_t floatChargeTime;
    uint8_t limit_max_temp;

    uint8_t pos;

    uint16_t coinChargeTime;
    uint16_t cardChargeTime;

    uint16_t level1MaxChargePower;
    uint16_t level2MaxChargePower;
    uint16_t level3MaxChargePower;
    uint16_t level4MaxChargePower;
    uint16_t limit_power;
    uint16_t limit_total_charge_power;
    uint16_t detect_timeout_time;
    uint16_t start_check_time;
};
static struct Set_t set;

// 测试参数
struct Test_t
{

    uint8_t time;
    uint8_t last_port;
    uint8_t flash_cnt;

    uint8_t isTest : 1;
    uint8_t isConnect : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
};
static struct Test_t test, test_tab[TOTAL_PORTS_NUM];

// 投币参数
struct coin_t
{

    uint16_t max_charge_time;
    uint16_t totalCoinAmount;
    uint8_t coins;

    uint8_t isCoin : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
};
static struct coin_t coin;

// 校准参数
struct Calibrate_t
{

    uint16_t isOver;
    uint16_t ref_P;
    uint16_t ref_E[10];
    uint16_t ref_F[10];

    uint8_t page_cnt;

    uint8_t isCalibrate : 1;
    uint8_t isRefPowerAdjust : 1;
    uint8_t isCalibrating : 1;
    uint8_t isCalibrateComplete : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
};
static struct Calibrate_t calibrate;

// 清零参数
struct clear_t
{
    uint8_t isClear : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
};
static struct clear_t clear;

// 全局变量
struct Global_t
{
    uint16_t port_error_state;
    uint16_t lastAmount;
    uint16_t upload_timer_cnt;
    uint16_t increased_coins;
    uint16_t increased_card_amount;
    uint16_t increased_electricity;
    uint16_t increased_charge_time;

    uint8_t back_timer;
    uint8_t flip;
    uint8_t lastChargePort;
    uint8_t lastPayType;

    uint8_t stop_disp_flag : 1;
    uint8_t reChoosePort : 1;
    uint8_t isErrorSta : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
    uint8_t : 1;
};
static struct Global_t global;

// 断电保存的参数
struct Outage_preservation_t
{
    uint16_t port_enable_state;
    uint16_t local_card_pay_state;
    uint16_t online_card_pay_state;
    uint16_t coin_pay_state;
    uint16_t remote_pay_state;
    uint16_t port_lock_state;
};
static struct Outage_preservation_t power_outage_parameters;

static struct Charge_t charge;
static struct WorkState_t workStates[TOTAL_PORTS_NUM];

// 设置界面的抬头显示
static const uint8_t SetTitle[28] = {11, 12, 21, 22, 31, 41, 42, 43, 44, 51, 52, 53, 61, 62, 66, 71, 72, 73, 74, 81, 82, 83, 84, 85, 86, 91, 92, 93};
// 参数加减用的参数
static const uint8_t adddata[23] = {30, 30, 1, 1, 1, 50, 50, 50, 50, 1, 1, 1, 0, 0, 0, 0, 10, 30, 0, 10, 10, 10, 10};
static const uint8_t subdata[23] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 1, 1};
// 参数限制值
static const uint16_t limitdata[23] = {990, 990, 50, 50, 50, 3500, 3500, 3500, 3500, 100, 100, 100, 0, 0, 0, 0, 100, 240, 0, 150, 999, 7200, 999};
// 参数保存在epprom的位置                         0 1  2  3  4 5 6 7  8  9 10 11 12 13 14 15 16 17 18 19 20 21 22
static const uint8_t sys_para_eeprom_addr[25] = {1, 3, 23, 24, 25, 5, 7, 9, 11, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 13, 15, 17, 19, 21};

static StopReason stop_reason;
static uint16_t before_stop_remain_time = 0;
static uint16_t before_stop_remain_battery = 0;

void save_remote_sys_parameters(uint16_t *ptr)
{
    uint8_t i = 0;
    uint16_t *p;

    p = ptr;

    for (i = 0; i < 23; i++)
    {
        rw_sys_parameters(WRITE, i, p[i]);
        save_sys_parameters(sys_para_eeprom_addr[i], p[i]);
    }
}

// 初始化设置系统参数
void init_sys_parameters(uint8_t x, uint16_t temp)
{
    switch (x)
    {
    case 0:
        set.coinChargeTime = temp;
        break;
    case 1:
        set.cardChargeTime = temp;
        break;
    case 2:
        set.level1MaxChargePower = temp;
        break;
    case 3:
        set.level2MaxChargePower = temp;
        break;
    case 4:
        set.level3MaxChargePower = temp;
        break;
    case 5:
        set.level4MaxChargePower = temp;
        break;
    case 6:
        set.limit_total_charge_power = temp;
        break;
    case 7:
        set.detect_timeout_time = temp;
        break;
    case 8:
        set.start_check_time = temp;
        break;
    case 9:
        if (temp >= 0xffff)
            temp = 0;
        coin.totalCoinAmount = temp;
        break;
    case 10:
        if (temp >= 0xffff)
            temp = 0;
        set_card_totalAmount(temp);
        break;
    case 11:
        set.coinMaxPowerConsumption = temp;
        break;
    case 12:
        set.cardMaxPowerConsumption = temp;
        break;
    case 13:
        set.cardDeductionAmount = temp;
        break;
    case 14:
        set.level2ChargePercentage = temp;
        break;
    case 15:
        set.level3ChargePercentage = temp;
        break;
    case 16:
        set.level4ChargePercentage = temp;
        break;
    case 17:
        set.allowedRecovery = temp;
        break;
    case 18:
        set.billing_method = temp;
        break;
    case 19:
        set.freecharge = temp;
        if (set.freecharge != 0)
        {
            COIN_DISABLE;
            set_local_card_state(1);
            set_online_card_state(1);
        }
        else
        {
            COIN_ENABLE;
            set_local_card_state(0);
            set_online_card_state(0);
        }
        break;
    case 20:
        set.allowedSelfStop = temp;
        break;
    case 21:
        set.maxFloatPower = temp;
        break;
    case 22:
        set.floatChargeTime = temp;
        break;
    case 23:
        set.initDisplay = temp;
        break;
    case 24:
        set.limit_max_temp = temp;
        break;
    default:
        break;
    }
}

// 读写系统参数
uint16_t rw_sys_parameters(uint8_t sta, uint8_t x, uint16_t temp)
{
    uint8_t i = 0;
    uint16_t value = 0;

    switch (x)
    {
    case 0:
        if (sta != 0)
            value = set.coinChargeTime;
        else
            set.coinChargeTime = temp;
        break;
    case 1:
        if (sta != 0)
            value = set.cardChargeTime;
        else
            set.cardChargeTime = temp;
        break;
    case 2:
        if (sta != 0)
            value = set.coinMaxPowerConsumption;
        else
            set.coinMaxPowerConsumption = temp;
        break;
    case 3:
        if (sta != 0)
            value = set.cardMaxPowerConsumption;
        else
            set.cardMaxPowerConsumption = temp;
        break;
    case 4:
        if (sta != 0)
            value = set.cardDeductionAmount;
        else
            set.cardDeductionAmount = temp;
        break;
    case 5:
        if (sta != 0)
            value = set.level1MaxChargePower;
        else
            set.level1MaxChargePower = temp;
        break;
    case 6:
        if (sta != 0)
            value = set.level2MaxChargePower;
        else
            set.level2MaxChargePower = temp;
        break;
    case 7:
        if (sta != 0)
            value = set.level3MaxChargePower;
        else
            set.level3MaxChargePower = temp;
        break;
    case 8:
        if (sta != 0)
            value = set.level4MaxChargePower;
        else
            set.level4MaxChargePower = temp;
        break;
    case 9:
        if (sta != 0)
            value = set.level2ChargePercentage;
        else
            set.level2ChargePercentage = temp;
        break;
    case 10:
        if (sta != 0)
            value = set.level3ChargePercentage;
        else
            set.level3ChargePercentage = temp;
        break;
    case 11:
        if (sta != 0)
            value = set.level4ChargePercentage;
        else
            set.level4ChargePercentage = temp;
        break;
    case 12:
        if (sta != 0)
            value = set.allowedRecovery;
        else
            set.allowedRecovery = temp;
        break;
    case 13:
        if (sta != 0)
            value = set.billing_method;
        else
            set.billing_method = temp;
        break;
    case 14:
        if (sta != 0)
            value = set.freecharge;
        else
        {
            set.freecharge = temp;
            if (set.freecharge != 0)
            {
                COIN_DISABLE;
                set_local_card_state(1);
                set_online_card_state(1);
            }
            else
            {
                COIN_ENABLE;
                set_local_card_state(0);
                set_online_card_state(0);
            }
        }
        break;
    case 15:
        if (sta != 0)
            value = set.allowedSelfStop;
        else
        {
            set.allowedSelfStop = temp;
            if (temp != 0)
            {
                for (i = 0; i < TOTAL_PORTS_NUM; i++)
                {
                    if (get_charge_state(i) != 0)
                    {
                        set_stop_recal_flag(i, 1);
                    }
                }
            }
            else
            {
                for (i = 0; i < TOTAL_PORTS_NUM; i++)
                {
                    set_stop_recal_flag(i, 0);
                }
            }
        }
        break;
    case 16:
        if (sta != 0)
            value = set.maxFloatPower;
        else
            set.maxFloatPower = temp;
        break;
    case 17:
        if (sta != 0)
            value = set.floatChargeTime;
        else
            set.floatChargeTime = temp;
        break;
    case 18:
        if (sta != 0)
            value = set.initDisplay;
        else
            set.initDisplay = temp;
        break;
    case 19:
        if (sta != 0)
            value = set.limit_max_temp;
        else
            set.limit_max_temp = temp;
        break;
    case 20:
        if (sta != 0)
            value = set.limit_total_charge_power;
        else
            set.limit_total_charge_power = temp;
        break;
    case 21:
        if (sta != 0)
            value = set.detect_timeout_time;
        else
            set.detect_timeout_time = temp;
        break;
    case 22:
        if (sta != 0)
            value = set.start_check_time;
        else
            set.start_check_time = temp;
        break;
    case 23:
        value = get_temp_value();
        break;
    case 24:
        if (sta != 0)
            value = coin.totalCoinAmount;
        else
        {
            if (temp >= 0xffff)
                temp = 0;
            coin.totalCoinAmount = temp;
        }
        break;
    case 25:
        if (sta != 0)
            value = get_card_totalAmount();
        else
        {
            if (temp >= 0xffff)
                temp = 0;
            set_card_totalAmount(temp);
        }
        break;
    default:
        break;
    }

    return value;
}

// 获取不同等级的百分比
uint16_t get_level_percentage(uint8_t level)
{
    uint16_t p = 0;

    switch (level)
    {
    case 2:
        p = set.level2ChargePercentage;
        break;
    case 3:
        p = set.level3ChargePercentage;
        break;
    case 4:
        p = set.level4ChargePercentage;
        break;
    default:
        break;
    }

    return p;
}

// 获取不同等级的限制功率
uint16_t get_level_max_power(uint8_t level)
{
    uint16_t p = 0;

    switch (level)
    {
    case 1:
        p = set.level1MaxChargePower;
        break;
    case 2:
        p = set.level2MaxChargePower;
        break;
    case 3:
        p = set.level3MaxChargePower;
        break;
    case 4:
        p = set.level4MaxChargePower;
        break;
    default:
        break;
    }

    return p;
}

uint8_t get_billing_method(void)
{
    return set.billing_method;
}

uint16_t get_limit_total_charge_power(void)
{
    return set.limit_total_charge_power;
}

uint8_t get_limited_temp(void)
{
    return set.limit_max_temp;
}

uint8_t get_freeCharge_sta(void)
{
    return set.freecharge;
}

uint16_t get_port_card_last_amount(uint8_t x)
{
    return workStates[x].charge.last_card_amount;
}

uint32_t get_port_card_serilanum(uint8_t x)
{
    return workStates[x].charge.card_serialNum;
}

uint8_t get_last_charge_port(void)
{
    return global.lastChargePort;
}

uint16_t get_increased_coins(void)
{
    return global.increased_coins;
}

uint16_t get_increased_card_amount(void)
{
    return global.increased_card_amount;
}

uint16_t get_increased_charge_time(void)
{
    return global.increased_charge_time;
}

uint16_t get_increased_electricity(void)
{
    return global.increased_electricity;
}

Consume get_last_paytype(void)
{
    return global.lastPayType;
}

uint16_t get_port_error_state(void)
{
    return global.port_error_state;
}

uint16_t get_port_lock_state(void)
{
    return power_outage_parameters.port_lock_state;
}

uint16_t get_before_stop_remain_time(void)
{
    return before_stop_remain_time;
}

uint16_t get_before_stop_remain_battery(void)
{
    return before_stop_remain_battery;
}

uint16_t get_charge_coin_paytype_state(void)
{
    return power_outage_parameters.coin_pay_state;
}

uint16_t get_charge_local_card_paytype_state(void)
{
    return power_outage_parameters.local_card_pay_state;
}

uint16_t get_charge_online_card_paytype_state(void)
{
    return power_outage_parameters.online_card_pay_state;
}

uint16_t get_charge_remote_paytype_state(void)
{
    return power_outage_parameters.remote_pay_state;
}

// 获取充电端口关闭原因
StopReason get_charge_port_stop_reason(void)
{
    return stop_reason;
}

// 设置充电功率
uint16_t get_charge_power_value(uint8_t x)
{
    return workStates[x].charge.chargePower;
}

// 获取总的投币金额
uint16_t get_coin_totalAmount(void)
{
    return coin.totalCoinAmount;
}

// 获取被使能的充电端口
uint16_t get_charge_enable_state(void)
{
    return power_outage_parameters.port_enable_state;
}

// 获取端口的工作状态
ChargeState get_charge_disp_state(uint8_t x)
{
    return workStates[x].charge.state;
}

// 获取上一次保存的总的充电时间
uint16_t get_last_total_charge_time(uint8_t x)
{
    return workStates[x].charge.lastTotalChargeTime;
}

// 获取总的充电时间
uint16_t get_charge_total_time(uint8_t x)
{
    return workStates[x].charge.totalChargeTime;
}

// 获取上次保存的充电时间
uint16_t get_last_charge_time(uint8_t x)
{
    return workStates[x].charge.lastChargeTime;
}

// 获取总的充电金额
uint16_t get_charge_total_amount(uint8_t x)
{
    return workStates[x].charge.totalAmount;
}

// 获取剩余充电金额
uint16_t get_charge_remain_amount(uint8_t x)
{
    return workStates[x].charge.remainAmount;
}

// 获取剩余充电电量
uint16_t get_charge_remain_battery(uint8_t x)
{
    return workStates[x].charge.remainBattery;
}

// 获取端口总的充电电量
uint16_t get_charge_total_energies(uint8_t x)
{
    return workStates[x].charge.totalEnergyConsumption;
}

// 获取当前的剩余充电时间
uint16_t get_charge_times(uint8_t x)
{
    return workStates[x].charge.chargeTimes;
}
// 获取浮充时间
uint16_t get_float_charge_time(void)
{
    return set.floatChargeTime;
}

// 获取最大浮充功率值
uint16_t get_max_float_charge_value(void)
{
    return set.maxFloatPower;
}
// 获取浮充状态
uint8_t get_floatcharge_state(uint8_t x)
{
    return workStates[x].charge.isFloatCharge;
}

// 获取最大限制功率值
uint16_t get_limited_power_value(void)
{
    if (set.level1MaxChargePower == set.level2MaxChargePower == set.level3MaxChargePower == set.level4MaxChargePower)
    {
        set.limit_power = set.level1MaxChargePower;
    }
    else
    {
        set.limit_power = set.level4MaxChargePower;
    }

    return set.limit_power;
}

uint8_t get_error_sta(void)
{
    return global.isErrorSta;
}

uint16_t get_last_charge_amount(void)
{
    return global.lastAmount;
}

uint8_t get_rechoose_state(void)
{
    return global.reChoosePort;
}

// 获取余额回收标志
uint8_t get_allowrecovery_state(void)
{
    return set.allowedRecovery;
}

// 获取未连接时的状态标志
uint8_t get_charge_noconnect_state(uint8_t x)
{
    return workStates[x].charge.isNoConnect;
}

// 获取充满自停状态
uint8_t get_selfstop_state(void)
{
    return set.allowedSelfStop;
}

// 获取功率超限状态
uint8_t get_error_highpower_state(uint8_t x)
{
    return workStates[x].charge.isHighPower;
}

uint8_t get_charge_check_cnt(uint8_t x)
{
    return workStates[x].charge.check_cnt;
}

// 获取充电端口的状态
uint8_t get_charge_state(uint8_t x)
{
    return workStates[x].charge.isCharge;
}

// 获取测试状态
uint8_t get_test_state(uint8_t x)
{
    return test_tab[x].isTest;
}

// 获取校准状态
uint8_t get_calibrate_state(uint8_t x)
{
    return workStates[x].isAjustState;
}

// 获取溢出标志位值
uint16_t get_calibrate_ref_over_value(void)
{
    return calibrate.isOver;
}

// 获取校准用的参考电量值
uint16_t get_calibrate_ref_energy_value(uint8_t x)
{
    return calibrate.ref_E[x];
}

// 获取校准用的参考频率值
uint16_t get_calibrate_ref_freq_value(uint8_t x)
{
    return calibrate.ref_F[x];
}
// 获取校准用的参考功率值
uint16_t get_calibrate_ref_power_value(void)
{
    return calibrate.ref_P;
}

// 获取单次刷卡增加的充电时间
uint16_t get_ic_card_charge_time_value(void)
{
    return set.cardChargeTime;
}

// 获取单次刷卡消费金额
uint16_t get_ic_card_deduction_amount(void)
{
    return set.cardDeductionAmount;
}

// 获取刷卡类型
Consume get_charge_paytype(uint8_t x)
{
    return workStates[x].charge.payType;
}

// 获取重新检测功率的状态
uint8_t get_charge_recheck_state(uint8_t x)
{
    return workStates[x].charge.isRecheckPower;
}

// 获取重新充电连接的状态
uint8_t get_charge_reconnect_state(uint8_t x)
{
    return workStates[x].charge.isReconnect;
}

uint8_t get_manual_recovery_amount_flag(uint8_t x)
{
    return workStates[x].charge.manual_recovery_amount_flag;
}

uint16_t get_detect_timeout_time(void)
{
    return set.detect_timeout_time;
}

// 设置返回主界面的倒计时时间
void set_back_timer_value(uint8_t temp)
{
    global.back_timer = temp;
}

// 设置校准用的电量参考值
void set_ref_energy_value(uint8_t x, uint16_t temp)
{
    calibrate.ref_E[x] = temp;
}

// 设置溢出标志位值
void set_calibrate_ref_over_value(uint16_t temp)
{
    calibrate.isOver = temp;
}

// 设置校准完成标志
void set_calibrate_complete_state(uint8_t sta)
{
    calibrate.isCalibrateComplete = sta;
}

// 设置充电功率
void set_charge_power_value(uint8_t x, uint16_t temp)
{
    workStates[x].charge.chargePower = temp;
}

void set_charge_enable_flag(uint8_t x, uint8_t sta)
{
    workStates[x].charge.isCharge = sta;
}

// 设置当前端口的工作状态
void set_charge_state(uint8_t x, ChargeState sta)
{
    workStates[x].charge.state = sta;
}

// 设置端口显示状态
void set_charge_work_state(uint8_t x, WorkState sta)
{
    workStates[x].work = sta;
}

// 设置发生错误时的倒计时计数器
void set_error_cnt(uint8_t x, uint8_t temp)
{
    workStates[x].charge.error_cnt = temp;
}

// 设置重新检测功率的状态
void set_charge_recheck_state(uint8_t x, uint8_t sta)
{
    workStates[x].charge.isRecheckPower = sta;
}

// 设置重新充电连接的状态
void set_charge_reconnect_state(uint8_t x, uint8_t sta)
{
    workStates[x].charge.isReconnect = sta;
}

// 设置未连接时的状态标志
void set_charge_noconnect_state(uint8_t x, uint8_t sta)
{
    workStates[x].charge.isNoConnect = sta;
}

// 设置功率超限状态
void set_error_highpower_state(uint8_t x, uint8_t sta)
{
    workStates[x].charge.isHighPower = sta;
}

// 设置浮充状态
void set_floatcharge_state(uint8_t x, uint8_t sta)
{
    workStates[x].charge.isFloatCharge = sta;
}

// 设置付费类型
void set_charge_paytype(uint8_t x, Consume sta)
{
    workStates[x].charge.payType = sta;
}

// 设置时间计数器
void set_charge_minites(uint8_t x, uint8_t temp)
{
    workStates[x].charge.minites = temp;
}

// 设置总充电时间
void set_charge_total_time(uint8_t x, uint16_t temp)
{
    workStates[x].charge.totalChargeTime = temp;
    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + (x * 2), temp);
}

// 设置保存上次的充电时间
void set_last_charge_time(uint8_t x, uint16_t temp)
{
    workStates[x].charge.lastChargeTime = temp;
}

// 设置保存上次的总充电时间
void set_last_total_charge_time(uint8_t x, uint16_t temp)
{
    workStates[x].charge.lastTotalChargeTime = temp;
    //    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR+(x*2), temp);
}

// 设置充电时间
void set_charge_times(uint8_t x, uint16_t temp)
{
    workStates[x].charge.chargeTimes = temp;
    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 60 + (x * 2), temp);
}

void set_charge_total_battery(uint8_t x, uint16_t temp)
{
    workStates[x].charge.totalEnergyConsumption = temp;
}

void set_charge_total_amount(uint8_t x, uint16_t temp)
{
    workStates[x].charge.totalAmount = temp;
}

// 设置剩余充电电量
void set_charge_remain_battery(uint8_t x, uint16_t temp)
{
    uint32_t temp_value = 0;

    workStates[x].charge.remainBattery = temp;
    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 80 + (x * 2), temp);
    // 计算剩余可用余额
    if (set.billing_method == 0)
    {
        temp_value = workStates[x].charge.remainBattery * 100;
        temp_value /= workStates[x].charge.totalEnergyConsumption;
        workStates[x].charge.remainAmount = (workStates[x].charge.totalAmount * temp_value) / 100;
        save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 100 + (x * 2), workStates[x].charge.remainAmount);
    }
}

// 设置剩余充电金额
void set_charge_remain_amount(uint8_t x, uint16_t temp)
{
    workStates[x].charge.remainAmount = temp;
}

void set_charge_check_cnt(uint8_t x, uint8_t temp)
{
    workStates[x].charge.check_cnt = temp;
}

// 设置充电端口关闭原因
void set_charge_port_stop_reason(StopReason reason)
{
    stop_reason = reason;
}

void set_before_stop_remain_time(uint16_t temp)
{
    before_stop_remain_time = temp;
}

void set_before_stop_remain_battery(uint16_t temp)
{
    before_stop_remain_battery = temp;
}

// 设置测试所用的时间
void set_test_times(uint8_t temp)
{
    test.time = temp;
}

// 设置测试的状态
void set_test_state(uint8_t x, uint8_t sta)
{
    test_tab[x].isTest = sta;
}

// 设置测试有负载连接上的状态
void set_test_connected_state(uint8_t x, uint8_t sta)
{
    test_tab[x].isConnect = sta;
}

// 设置总的充电时间
void set_total_charge_time(uint8_t x, uint16_t temp)
{
    workStates[x].charge.totalChargeTime = temp;
}

// 设置校准参考频率
void set_calibrate_ref_freq_parameters(uint8_t x, uint16_t temp)
{
    calibrate.ref_F[x] = temp;
}

// 设置校准参考电量参数
void set_calibrate_ref_energy_parameters(uint8_t x, uint16_t temp)
{
    calibrate.ref_E[x] = temp;
}

// 设置校准参考功率值
void set_calibrate_ref_power_parameters(uint16_t temp)
{
    calibrate.ref_P = temp;
}

void set_coin_coins(uint8_t temp)
{
    coin.coins = temp;
}

void set_coin_enable_state(uint8_t sta)
{
    coin.isCoin = sta;
}

void set_rechoose_state(uint8_t sta)
{
    global.reChoosePort = sta;
}

void set_stop_disp_flag(uint8_t sta)
{
    global.stop_disp_flag = sta;
}

void set_port_error_state(uint8_t sta, uint8_t x)
{
    if (sta != 0)
        global.port_error_state |= (1 << x);
    else
        global.port_error_state &= ~(1 << x);
}

void set_last_charge_port(uint8_t x)
{
    global.lastChargePort = x;
}

void set_last_charge_amount(uint16_t x)
{
    global.lastAmount = x;
}

void set_last_paytype(Consume type)
{
    global.lastPayType = type;
}

void set_error_sta(uint8_t sta)
{
    global.isErrorSta = sta;
}

void set_port_lock_state(uint8_t sta, uint16_t temp)
{
    if (sta != 0)
        power_outage_parameters.port_lock_state |= temp;
    else
        power_outage_parameters.port_lock_state &= ~temp;
}

// 设置断电前的充电状态
void set_charge_port_enable_state(uint8_t sta, uint16_t temp)
{
    if (sta != 0)
        power_outage_parameters.port_enable_state |= temp;
    else
        power_outage_parameters.port_enable_state &= ~temp;
}

void set_charge_local_card_paytype_state(uint8_t sta, uint16_t temp)
{
    if (sta != 0)
        power_outage_parameters.local_card_pay_state |= temp;
    else
        power_outage_parameters.local_card_pay_state &= ~temp;
}

void set_charge_online_card_paytype_state(uint8_t sta, uint16_t temp)
{
    if (sta != 0)
        power_outage_parameters.online_card_pay_state |= temp;
    else
        power_outage_parameters.online_card_pay_state &= ~temp;
}

void set_charge_coin_paytype_state(uint8_t sta, uint16_t temp)
{
    if (sta != 0)
        power_outage_parameters.coin_pay_state |= temp;
    else
        power_outage_parameters.coin_pay_state &= ~temp;
}

void set_charge_remote_paytype_state(uint8_t sta, uint16_t temp)
{
    if (sta != 0)
        power_outage_parameters.remote_pay_state |= temp;
    else
        power_outage_parameters.remote_pay_state &= ~temp;
}

void set_upload_all_timer(uint16_t temp)
{
    global.upload_timer_cnt = temp;
}

void set_manual_recovery_amount_flag(uint8_t x, uint8_t temp)
{
    workStates[x].charge.manual_recovery_amount_flag = temp;
}

// 对充电端口参数复位
void set_charge_reset(uint8_t x)
{
    set_detect_state(x, 0);
    set_ic_card_swiped(0);

    workStates[x].charge.last_card_amount = 0;
    workStates[x].charge.card_serialNum = 0;

    workStates[x].charge.chargeTimes = 0;
    workStates[x].charge.chargePower = 0;   // 充电功率
    workStates[x].charge.remainBattery = 0; // 剩余电量
    workStates[x].charge.remainAmount = 0;  // 剩余金额

    workStates[x].charge.totalChargeTime = 0;
    workStates[x].charge.totalEnergyConsumption = 0;
    workStates[x].charge.totalAmount = 0;

    workStates[x].charge.lastChargeTime = 0;
    workStates[x].charge.lastTotalChargeTime = 0;
    workStates[x].charge.last_remain_amount = 0;

    workStates[x].charge.minites = 0;
    workStates[x].charge.check_cnt = 0;
    workStates[x].charge.error_cnt = 0;
    workStates[x].charge.back_cnt = 0;

    workStates[x].charge.payType = NO_TYPE;
    workStates[x].charge.state = IDLE_STA;
    workStates[x].work = NoState;

    workStates[x].isAjustState = 0;
    workStates[x].charge.isCharge = 0;
    workStates[x].charge.isFloatCharge = 0;
    workStates[x].charge.isNoConnect = 0;
    workStates[x].charge.isHighPower = 0;
    workStates[x].charge.isReconnect = 0;
    workStates[x].charge.isRecheckPower = 0;

    set_stop_recal_flag(x, 0);
    set_detect_power_cnt(x, 0);
    set_detect_overtime(x, 0);
    set_detect_reserve_max_power(x, 0);
    set_detect_last_max_power(x, 0);
    set_remote_sucess_flag(x, 0);
    set_start_check_charge_port(x, 0);
    set_end_check_charge_port(x, 0);
    set_charge_time_level(x, 0);
    set_dynamic_total_charge_time(x, 0);
    set_power_safe_flag(0);

    power_outage_parameters.port_enable_state &= ~(1 << x);
    power_outage_parameters.local_card_pay_state &= ~(1 << x);
    power_outage_parameters.online_card_pay_state &= ~(1 << x);
    power_outage_parameters.coin_pay_state &= ~(1 << x);
    power_outage_parameters.remote_pay_state &= ~(1 << x);

    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + (x * 2), 0);
    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 20 + (x * 2), 0);
    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 40 + (x * 2), 0);

    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 60 + (x * 2), 0);
    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 80 + (x * 2), 0);
    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 100 + (x * 2), 0);

    save_parameters(OUTAGE_FLAG_16BIT_ADDR, power_outage_parameters.port_enable_state);
    save_parameters(OUTAGE_FLAG_16BIT_ADDR + 2, power_outage_parameters.local_card_pay_state);
    save_parameters(OUTAGE_FLAG_16BIT_ADDR + 4, power_outage_parameters.online_card_pay_state);
    save_parameters(OUTAGE_FLAG_16BIT_ADDR + 6, power_outage_parameters.coin_pay_state);
    save_parameters(OUTAGE_FLAG_16BIT_ADDR + 8, power_outage_parameters.remote_pay_state);
}

// 关停指定的充电端口
void stop_charge_port(uint8_t x)
{
    if (set.freecharge == 0)
    {
        COIN_ENABLE;
        set_local_card_state(0);
        set_online_card_state(0);
    }
    else
    {
        COIN_DISABLE;
        set_local_card_state(1);
        set_online_card_state(1);
    }

    set_charge_reset(x);

    relay_write(x, GPIO_PIN_RESET);

    init_charge_rom(x, D_NORMAL_MODE);
    reset_filter_data(x);
}

// 给gprs邮箱发送命令
void send_gprs_mb_cmd(uint8_t cmd, uint8_t port)
{
    rt_mb_send(&gprs_transmit_mb, (uint32_t)((cmd << 8) | port));
}

// 主界面显示
void main_view(struct disp_t *pdisp, uint8_t i)
{
    //    uint8_t i = 0;

    //    for (i = 0; i < TOTAL_PORTS_NUM; i++)
    //    {
    switch (workStates[i].charge.state)
    {
    case IDLE_STA:
        pdisp->type = Idle;
        pdisp->data = i + 1;
        break;
    case LOCK_STA:
        pdisp->type = Error;
        pdisp->data = DISP_ERROR_NUM;
        break;
    case HIGH_STA:
        pdisp->type = Error;
        pdisp->data = DISP_HIGH_NUM;
        break;
    case ERROR_STA:
        pdisp->type = Error;
        pdisp->data = DISP_ERROR_NUM;
        break;
    default:
        if ((test_tab[i].isTest != 0 && test.time != 0) || test_tab[i].isConnect != 0)
        {
            workStates[i].work = TestState;
            workStates[i].charge.state = NOMAL_STA;
            pdisp->type = Idle;
            pdisp->data = i + 1;
        }
        else
        {
            if (get_start_check_charge_port(i) != 0)
            {
                pdisp->type = Check_power;
                pdisp->data = workStates[i].charge.chargeTimes;
            }
            else
            {
                pdisp->type = Parameters;
                switch (workStates[i].charge.check_cnt)
                {
                case 1:
                    pdisp->data = workStates[i].charge.chargePower;
                    break;
                case 2:
                    if (set.initDisplay != 0)
                        pdisp->data = workStates[i].charge.remainBattery;
                    else
                        pdisp->data = workStates[i].charge.chargeTimes;
                    break;
                case 3:
                    pdisp->data = workStates[i].charge.remainAmount;
                    break;
                default:
                    if (set.initDisplay != 0)
                        pdisp->data = workStates[i].charge.remainBattery;
                    else
                        pdisp->data = workStates[i].charge.chargeTimes;
                    break;
                }
            }
        }
        break;
    }

    pdisp->addr = i;
    pdisp->point = No_Point;
    pdisp->hide = No_Hide;
    auto_show_digitaltube(pdisp);
    //    }
}

// 投币模式
static void coinFun(struct disp_t *pdisp)
{
    t_voice_data voice_data;
    uint16_t temp = 0;
    uint8_t i, flag = 0;

    if ((1000 - coin.max_charge_time) < set.coinChargeTime)
    {
        return;
    }
    else
    {
        if (global.reChoosePort != 0 && global.lastPayType == COIN_TYPE)
            coin.coins = global.lastAmount / 10;
        if (++coin.coins >= 100)
            coin.coins = 100;
        coin.max_charge_time = coin.coins * set.coinChargeTime;
        global.lastAmount = coin.coins * 10;
    }

    set_local_card_state(1);
    set_online_card_state(1);

    set_card_swipe_times(0);

    coin.isCoin = 1;

    global.stop_disp_flag = 1;
    global.back_timer = 10;
    //    global.lastPayType = COIN_TYPE;

    //    speeker(Voice_Commd_Coin_Successfully);
    voice_data.type = Voice_Commd_Coin_Successfully;
    voice_data.power = 0;
    voice_data.time = 0;
    voice_data.balance = 0;
    spell_voice(&voice_data);

    if (global.isErrorSta != 0)
    {
        COIN_ENABLE;
        set_local_card_state(0);
        set_online_card_state(0);
    }

    seg_clear();

    pdisp->type = Parameters;
    pdisp->addr = 0;
    pdisp->data = coin.coins;
    pdisp->point = No_Point;
    pdisp->hide = No_Hide;
    auto_show_digitaltube(pdisp);

    if (global.reChoosePort != 0 && global.lastPayType == COIN_TYPE)
    {
        return;
    }
    else
    {
        global.reChoosePort = 0;

        set_local_card_swipe_state(0);

        for (i = 0; i < TOTAL_PORTS_NUM; i++)
        {
            temp = global.port_error_state & (1 << i);
            if ((temp != 0) && (workStates[i].charge.payType == LOCAL_SWIPE_TYPE))
            {
                flag++;

                global.port_error_state &= ~(1 << i);

                set_detect_state(i, 0);

                set_charge_reset(i);

                relay_write(i, GPIO_PIN_RESET);

                init_charge_rom(i, D_NORMAL_MODE);
                reset_filter_data(i);
            }
        }
    }
    if (flag != 0)
    {
        set_card_swipe_times(0);
        save_parameters(OUTAGE_FLAG_16BIT_ADDR, power_outage_parameters.port_enable_state);
        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 2, power_outage_parameters.local_card_pay_state);
        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 4, power_outage_parameters.online_card_pay_state);
        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 6, power_outage_parameters.coin_pay_state);
        save_parameters(OUTAGE_FLAG_16BIT_ADDR + 8, power_outage_parameters.remote_pay_state);
    }
}

// 测试模式
static void test_fun(struct disp_t *pdisp, uint8_t x)
{
    t_voice_data voice_data;

    if (workStates[x - 1].charge.isCharge == 0 && get_local_card_swipe_state() == 0 && get_online_card_swipe_state() == 0 && coin.isCoin == 0 && global.reChoosePort == 0 && calibrate.page_cnt == 0 && workStates[x - 1].charge.manual_recovery_amount_flag == 0 && set.freecharge == 0 && global.stop_disp_flag == 0)
    {
        if (clear.isClear == 0 && calibrate.isCalibrate == 0 && set.isSet == 0)
        {
            if (workStates[x - 1].charge.state != LOCK_STA && workStates[x - 1].charge.state != ERROR_STA)
            {
                if (test.last_port == 0)
                {
                    global.flip = 0;
                    test.time = 10;
                    test.flash_cnt = 0;
                    test_tab[x - 1].isTest = 1;
                    test_tab[x - 1].isConnect = 0;
                    workStates[x - 1].work = TestState;
                    workStates[x - 1].charge.state = NOMAL_STA;

                    init_charge_rom(x - 1, D_NORMAL_MODE);
                    reset_filter_data(x - 1);

                    relay_write(x - 1, GPIO_PIN_SET);
                }
                else if (x != test.last_port)
                {
                    if (workStates[test.last_port - 1].charge.state != LOCK_STA && workStates[x - 1].charge.state != ERROR_STA)
                    {
                        test_tab[test.last_port - 1].isTest = 0;
                        test_tab[test.last_port - 1].isConnect = 0;
                        workStates[test.last_port - 1].work = NoState;
                        workStates[test.last_port - 1].charge.state = IDLE_STA;
                        pdisp->type = Idle;
                        pdisp->addr = test.last_port - 1;
                        pdisp->data = test.last_port;
                        pdisp->point = No_Point;
                        pdisp->hide = No_Hide;
                        auto_show_digitaltube(pdisp);

                        init_charge_rom(test.last_port - 1, D_NORMAL_MODE);
                        reset_filter_data(test.last_port - 1);

                        relay_write(test.last_port - 1, GPIO_PIN_RESET);
                    }

                    global.flip = 0;
                    test.time = 10;
                    test.flash_cnt = 0;
                    test_tab[x - 1].isTest = 1;
                    test_tab[x - 1].isConnect = 0;
                    workStates[x - 1].work = TestState;
                    workStates[x - 1].charge.state = NOMAL_STA;

                    init_charge_rom(x - 1, D_NORMAL_MODE);
                    reset_filter_data(x - 1);

                    relay_write(x - 1, GPIO_PIN_SET);
                }
                test.last_port = x;
            }
            else
            {
                //                speeker(Voice_Commd_Charge_Socket_Fauty);
                voice_data.type = Voice_Commd_Charge_Socket_Fauty;
                voice_data.power = 0;
                voice_data.time = 0;
                voice_data.balance = 0;
                spell_voice(&voice_data);

                test_tab[x - 1].isTest = 0;
            }
        }
    }
}

// 充电模式
static void charge_fun(struct disp_t *pdisp, uint8_t x)
{
    t_voice_data voice_data;
    uint8_t i, send_flag = 0, voice_cmmd = 0;
    uint16_t temp = 0;
    uint16_t total_card_amount = 0;

    if (test_tab[x - 1].isTest == 0 || set.freecharge != 0)
    {
        if (set.freecharge != 0)
        {
            if (get_power_safe_flag() != 0)
                return;

            if (workStates[x - 1].charge.isCharge == 0)
            {
                coin.isCoin = 1;
                coin.coins = 1;
            }
            else
            {
                coin.isCoin = 0;
                coin.coins = 0;
            }
        }
        // 充满自停结束后,有结余,则跳转到剩余金额界面
        if (workStates[x - 1].charge.manual_recovery_amount_flag != 0)
        {
            //            workStates[x-1].charge.manual_recovery_amount_flag = 0;

            if (get_local_card_swipe_state() == 0 && get_online_card_swipe_state() == 0 && coin.isCoin == 0)
            {
                COIN_ENABLE;
                set_online_card_state(0);
                set_local_card_state(0);

                set_back_timer_value(10);
                set_stop_disp_flag(1);

                seg_clear();

                //                set_port_error_state(true, x-1);
                //                set_last_charge_port(x-1);

                set_card_last_serialnum(workStates[x - 1].charge.card_serialNum);

                pdisp->data = workStates[x - 1].charge.remainAmount;

                if (pdisp->data < 1000)
                    pdisp->point = Pos_1;
                else
                {
                    pdisp->point = No_Point;
                    pdisp->data /= 10;
                }
                pdisp->type = Parameters;
                pdisp->addr = 0;
                pdisp->hide = No_Hide;
                auto_show_digitaltube(pdisp);

                if (workStates[x - 1].charge.last_card_amount < 1000)
                {
                    pdisp->point = Pos_1;
                    pdisp->data = workStates[x - 1].charge.last_card_amount;
                }
                else
                {
                    pdisp->point = No_Point;
                    pdisp->data = workStates[x - 1].charge.last_card_amount / 10;
                }
                pdisp->type = Parameters;
                pdisp->addr = 1;
                pdisp->hide = No_Hide;
                auto_show_digitaltube(pdisp);

                if (workStates[x - 1].charge.remainAmount == 0)
                {
                    workStates[x - 1].charge.manual_recovery_amount_flag = 0;
                    stop_charge_port(x - 1);
                    //                    speeker(Voice_Commd_Balance_Is_Zero);
                    voice_data.type = Voice_Commd_Balance_Is_Zero;
                    voice_data.power = 0;
                    voice_data.time = 0;
                    voice_data.balance = 0;
                    spell_voice(&voice_data);
                    return;
                }

                //                speeker(Voice_Commd_Card_Refund);
                voice_data.type = Voice_Commd_Card_Refund;
                voice_data.power = 0;
                voice_data.time = 0;
                voice_data.balance = 0;
                spell_voice(&voice_data);

                return;
            }
            else
            {
                workStates[x - 1].charge.manual_recovery_amount_flag = 0;
                stop_charge_port(x - 1);
            }
        }
        // 有投币或者刷卡，才可以开始充电
        if (((get_local_card_swipe_state() != 0 || get_online_card_swipe_state() != 0) && get_card_swipe_times() != 0) || (coin.isCoin != 0 && coin.coins != 0) || global.reChoosePort != 0)
        {
            // 如果当前端口进入了浮充状态,则不允许往此端口续费
            if (workStates[x - 1].charge.isFloatCharge != 0)
                return;
            // 如果当前端口处于远程控制状态,直接跳出当前程序
            if ((power_outage_parameters.remote_pay_state & (1 << (x - 1))) != 0)
                return;
            // 如果当前端口处于在线卡状态,本地投币和刷卡都不能对这路追加,直接跳出当前程序
            if ((get_local_card_swipe_state() != 0 || coin.isCoin != 0) && workStates[x - 1].charge.payType == ONLINE_SWIPE_TYPE)
                return;

            // 如果当前端口是投币状态，允许本地刷卡追加金额，将当前端口的付费状态改为刷卡
            if (get_local_card_swipe_state() != 0 && workStates[x - 1].charge.payType == COIN_TYPE)
            {
                set_local_card_swipe_state(0);
                workStates[x - 1].charge.payType = LOCAL_SWIPE_TYPE;
                workStates[x - 1].charge.last_card_amount = get_last_card_amount();
                workStates[x - 1].charge.card_serialNum = get_card_serial_num();
            }
            // 如果当前端口是本地刷卡状态，允许投币追加金额，将当前端口的付费状态改为投币
            else if (coin.isCoin != 0 && workStates[x - 1].charge.payType == LOCAL_SWIPE_TYPE)
            {
                coin.isCoin = 0;
                workStates[x - 1].charge.payType = COIN_TYPE;
            }

            global.isErrorSta = 0;
            global.stop_disp_flag = 0;
            global.back_timer = 0;

            test_tab[x - 1].isConnect = 0;
            test_tab[x - 1].isTest = 0;
            test.flash_cnt = 0;
            test.last_port = 0;

            if (set.freecharge == 0)
            {
                if (set.allowedSelfStop != 0)
                {
                    COIN_DISABLE;
                    set_local_card_state(1);
                    set_online_card_state(1);
                    set_detect_state(x - 1, 1);
                }
                else
                {
                    COIN_ENABLE;
                    set_local_card_state(0);
                    set_online_card_state(0);
                    set_detect_state(x - 1, 0);
                }
            }
            else
            {
                COIN_DISABLE;
                set_local_card_state(1);
                set_online_card_state(1);
            }

            set_ic_card_swiped(0);

            if (clear.isClear == 0 && set.isSet == 0 && test.isTest == 0 && calibrate.isCalibrate == 0)
            {
                if (workStates[x - 1].charge.state == ERROR_STA || workStates[x - 1].charge.state == LOCK_STA || workStates[x - 1].charge.isReconnect != 0 || workStates[x - 1].charge.isRecheckPower != 0)
                {
                    //                    speeker(Voice_Commd_Charge_Socket_Fauty);
                    voice_data.type = Voice_Commd_Charge_Socket_Fauty;
                    voice_data.power = 0;
                    voice_data.time = 0;
                    voice_data.balance = 0;
                    spell_voice(&voice_data);
                }
                else
                {
                    if (set.level1MaxChargePower == set.level2MaxChargePower == set.level3MaxChargePower == set.level4MaxChargePower)
                    {
                        set.limit_power = set.level1MaxChargePower;
                    }
                    else
                    {
                        set.limit_power = set.level4MaxChargePower;
                    }

                    if (global.reChoosePort != 0)
                    {
                        // 判断当前按下的端口是不是在正常充电,出错后重选的端口不能是正常在充电的端口
                        if (workStates[x - 1].charge.state == NOMAL_STA)
                            return;
                        global.reChoosePort = 0;
                        // 把上一个端口的参数转移到当前端口
                        if (workStates[global.lastChargePort].charge.payType == COIN_TYPE)
                        {
                            workStates[x - 1].charge.payType = COIN_TYPE;
                            power_outage_parameters.coin_pay_state |= (1 << (x - 1));
                            power_outage_parameters.local_card_pay_state &= ~(1 << (x - 1));
                            power_outage_parameters.online_card_pay_state &= ~(1 << (x - 1));
                            power_outage_parameters.remote_pay_state &= ~(1 << (x - 1));
                        }
                        else if (workStates[global.lastChargePort].charge.payType == LOCAL_SWIPE_TYPE)
                        {
                            workStates[x - 1].charge.last_card_amount = get_last_card_amount();
                            workStates[x - 1].charge.card_serialNum = get_card_serial_num();
                            workStates[x - 1].charge.payType = LOCAL_SWIPE_TYPE;
                            power_outage_parameters.local_card_pay_state |= 1 << (x - 1);
                            power_outage_parameters.online_card_pay_state &= ~(1 << (x - 1));
                            power_outage_parameters.coin_pay_state &= ~(1 << (x - 1));
                            power_outage_parameters.remote_pay_state &= ~(1 << (x - 1));
                        }
                        else if (workStates[global.lastChargePort].charge.payType == ONLINE_SWIPE_TYPE)
                        {
                            workStates[x - 1].charge.payType = ONLINE_SWIPE_TYPE;
                            power_outage_parameters.online_card_pay_state |= (1 << (x - 1));
                            power_outage_parameters.local_card_pay_state &= ~(1 << (x - 1));
                            power_outage_parameters.coin_pay_state &= ~(1 << (x - 1));
                            power_outage_parameters.remote_pay_state &= ~(1 << (x - 1));
                        }

                        power_outage_parameters.port_enable_state |= 1 << (x - 1);

                        if (get_local_card_swipe_state() != 0 || get_online_card_swipe_state() != 0 || coin.isCoin != 0)
                        {
                            if (get_local_card_swipe_state() != 0 || get_online_card_swipe_state() != 0)
                            {
                                set_local_card_state(0);
                                set_online_card_state(0);
                                // 总充电时间
                                temp = get_card_swipe_times() * set.cardChargeTime;
                                global.increased_charge_time = temp - (global.lastAmount / set.cardDeductionAmount) * set.cardChargeTime;
                                workStates[x - 1].charge.totalChargeTime = temp;
                                // 总充电电量
                                temp = get_card_swipe_times() * set.cardMaxPowerConsumption * 10;
                                global.increased_electricity = temp - (global.lastAmount / set.cardDeductionAmount) * set.cardMaxPowerConsumption * 10;
                                workStates[x - 1].charge.totalEnergyConsumption = temp;
                                // 总充电金额
                                temp = get_card_swipe_times() * set.cardDeductionAmount;
                                global.increased_card_amount = temp - global.lastAmount;
                                workStates[x - 1].charge.totalAmount = temp;
                                // 记录总的刷卡金额
                                total_card_amount = get_card_totalAmount();
                                total_card_amount += temp;
                                set_card_totalAmount(total_card_amount);
                                // 保存刷卡金额
                                if (get_local_card_swipe_state() != 0)
                                {
                                    if (total_card_amount >= 0xffff)
                                        total_card_amount = 0;
                                    save_sys_parameters(sys_para_eeprom_addr[TOTAL_SYS_PARA_NUM], total_card_amount);
                                }
                                set_card_swipe_times(0);
                                set_card_last_max_charge_time(0);
                            }
                            else if (coin.isCoin != 0)
                            {
                                coin.isCoin = 0;
                                // 总充电金额
                                global.increased_coins = coin.coins * 10 - global.lastAmount;
                                workStates[x - 1].charge.totalAmount = coin.coins * 10;
                                // 总充电时间
                                temp = coin.coins * set.coinChargeTime;
                                global.increased_charge_time = temp - (global.lastAmount / 10) * set.coinChargeTime;
                                workStates[x - 1].charge.totalChargeTime = temp;
                                // 总充电电量
                                temp = coin.coins * set.coinMaxPowerConsumption * 10;
                                global.increased_electricity = temp - (global.lastAmount / 10) * set.coinMaxPowerConsumption * 10;
                                workStates[x - 1].charge.totalEnergyConsumption = temp;

                                // 记录总的投币金额
                                coin.totalCoinAmount += coin.coins;
                                // 保存投币金额
                                save_sys_parameters(sys_para_eeprom_addr[TOTAL_SYS_PARA_NUM - 1], coin.totalCoinAmount);

                                coin.coins = 0;
                                coin.max_charge_time = 0;
                            }

                            if (workStates[x - 1].charge.totalChargeTime >= 1000)
                                workStates[x - 1].charge.totalChargeTime = 990;

                            workStates[x - 1].charge.chargeTimes = workStates[x - 1].charge.totalChargeTime;
                            workStates[x - 1].charge.remainAmount = workStates[x - 1].charge.totalAmount;
                            workStates[x - 1].charge.remainBattery = workStates[x - 1].charge.totalEnergyConsumption;

                            set_dynamic_total_charge_time(x - 1, workStates[x - 1].charge.totalChargeTime);
                        }
                        else
                        {
                            send_flag = 1;
                            workStates[x - 1].charge.totalAmount = workStates[global.lastChargePort].charge.totalAmount;
                            workStates[x - 1].charge.totalChargeTime = workStates[global.lastChargePort].charge.totalChargeTime;
                            workStates[x - 1].charge.totalEnergyConsumption = workStates[global.lastChargePort].charge.totalEnergyConsumption;
                            workStates[x - 1].charge.chargeTimes = workStates[global.lastChargePort].charge.chargeTimes;
                            workStates[x - 1].charge.remainAmount = workStates[global.lastChargePort].charge.remainAmount;
                            workStates[x - 1].charge.remainBattery = workStates[global.lastChargePort].charge.remainBattery;

                            set_dynamic_total_charge_time(x - 1, workStates[global.lastChargePort].charge.totalChargeTime);
                        }

                        global.port_error_state &= ~(1 << (x - 1));
                        // 对记录的最后一个错误端口之前的错误端口的参数清零
                        for (i = 0; i < TOTAL_PORTS_NUM; i++)
                        {
                            if ((global.port_error_state & (1 << i)) != 0)
                            {
                                global.port_error_state &= ~(1 << i);

                                set_charge_reset(i);

                                relay_write(i, GPIO_PIN_RESET);

                                init_charge_rom(i, D_NORMAL_MODE);
                                reset_filter_data(i);
                            }
                        }

                        global.lastChargePort = x - 1;
                        global.lastAmount = 0;

                        workStates[x - 1].work = NomalChargeState;
                        workStates[x - 1].charge.state = NOMAL_STA;
                        workStates[x - 1].charge.isCharge = 1;

                        set_detect_last_max_power(x - 1, 0);

                        init_charge_rom(x - 1, D_NORMAL_MODE);
                        reset_filter_data(x - 1);

                        relay_write(x - 1, GPIO_PIN_SET);

                        set_start_check_charge_port(x - 1, 1);
                        set_end_check_charge_port(x - 1, 1);
                        set_check_charge_port_cnt(x - 1, 1);

                        if (set.allowedSelfStop == 0)
                            voice_data.type = Voice_Commd_Start_Charge;
                        else
                            voice_data.type = Voice_Commd_Calculate_Charge_Time;
                        voice_data.power = 0;
                        voice_data.time = 0;
                        voice_data.balance = 0;
                        spell_voice(&voice_data);

                        for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                        {
                            main_view(pdisp, i);
                        }
                    }
                    else
                    {
                        // 在当前正常充电的端口上进行参数更改
                        switch (workStates[x - 1].charge.payType)
                        {
                        case COIN_TYPE:
                            coin.isCoin = 0;
                            // 总充电时间
                            if (set.allowedSelfStop != 0)
                            {
                                switch (get_charge_time_level(x - 1))
                                {
                                case 1:
                                    temp = ((coin.coins * set.coinChargeTime) * set.level2ChargePercentage) / 100;
                                    break;
                                case 2:
                                    temp = ((coin.coins * set.coinChargeTime) * set.level3ChargePercentage) / 100;
                                    break;
                                case 3:
                                    temp = ((coin.coins * set.coinChargeTime) * set.level4ChargePercentage) / 100;
                                    break;
                                default:
                                    temp = coin.coins * set.coinChargeTime;
                                    break;
                                }
                            }
                            else
                                temp = coin.coins * set.coinChargeTime;
                            global.increased_charge_time = temp;
                            workStates[x - 1].charge.totalChargeTime = workStates[x - 1].charge.chargeTimes + temp;
                            // 总充电电量
                            temp = coin.coins * set.coinMaxPowerConsumption * 10;
                            global.increased_electricity = temp;
                            workStates[x - 1].charge.totalEnergyConsumption = workStates[x - 1].charge.remainBattery + temp;
                            // 总充电金额
                            global.increased_coins = coin.coins * 10;
                            workStates[x - 1].charge.totalAmount = workStates[x - 1].charge.remainAmount + coin.coins * 10;
                            // 记录总的投币金额
                            coin.totalCoinAmount += coin.coins;
                            // 保存投币金额
                            save_sys_parameters(sys_para_eeprom_addr[TOTAL_SYS_PARA_NUM - 1], coin.totalCoinAmount);

                            coin.coins = 0;
                            coin.max_charge_time = 0;

                            for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                            {
                                main_view(pdisp, i);
                            }
                            break;
                        case LOCAL_SWIPE_TYPE:
                            // 在同一个端口上叠加金额必须是同一张卡
                            if (workStates[x - 1].charge.card_serialNum != get_card_serial_num())
                            {
                                for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                                {
                                    main_view(pdisp, i);
                                }
                                return;
                            }

                            workStates[x - 1].charge.last_card_amount = get_last_card_amount();
                            set_local_card_swipe_state(0);
                            // 总充电时间
                            if (set.allowedSelfStop != 0)
                            {
                                switch (get_charge_time_level(x - 1))
                                {
                                case 1:
                                    temp = ((get_card_swipe_times() * set.cardChargeTime) * set.level2ChargePercentage) / 100;
                                    break;
                                case 2:
                                    temp = ((get_card_swipe_times() * set.cardChargeTime) * set.level3ChargePercentage) / 100;
                                    break;
                                case 3:
                                    temp = ((get_card_swipe_times() * set.cardChargeTime) * set.level4ChargePercentage) / 100;
                                    break;
                                default:
                                    temp = get_card_swipe_times() * set.cardChargeTime;
                                    break;
                                }
                            }
                            else
                                temp = get_card_swipe_times() * set.cardChargeTime;
                            global.increased_charge_time = temp;
                            workStates[x - 1].charge.totalChargeTime = workStates[x - 1].charge.chargeTimes + temp;
                            // 总充电电量
                            temp = get_card_swipe_times() * set.cardMaxPowerConsumption * 10;
                            global.increased_electricity = temp;
                            workStates[x - 1].charge.totalEnergyConsumption = workStates[x - 1].charge.remainBattery + temp;
                            // 总充电金额
                            temp = get_card_swipe_times() * set.cardDeductionAmount;
                            global.increased_card_amount = temp;
                            workStates[x - 1].charge.totalAmount = workStates[x - 1].charge.remainAmount + temp;
                            // 记录总的刷卡金额
                            total_card_amount = get_card_totalAmount();
                            total_card_amount += temp;
                            set_card_totalAmount(total_card_amount);
                            // 保存刷卡金额
                            if (total_card_amount >= 0xffff)
                                total_card_amount = 0;
                            save_sys_parameters(sys_para_eeprom_addr[TOTAL_SYS_PARA_NUM], total_card_amount);

                            set_card_swipe_times(0);
                            set_card_last_max_charge_time(0);

                            for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                            {
                                main_view(pdisp, i);
                            }
                            break;
                        case ONLINE_SWIPE_TYPE:
                            // 在同一个端口上叠加金额必须是同一张卡
                            if (workStates[x - 1].charge.card_serialNum != get_card_serial_num())
                            {
                                for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                                {
                                    main_view(pdisp, i);
                                }
                                return;
                            }

                            workStates[x - 1].charge.last_card_amount = get_last_card_amount();
                            set_online_card_swipe_state(0);
                            // 总充电时间
                            if (set.allowedSelfStop != 0)
                            {
                                switch (get_charge_time_level(x - 1))
                                {
                                case 1:
                                    temp = ((get_card_swipe_times() * set.cardChargeTime) * set.level2ChargePercentage) / 100;
                                    break;
                                case 2:
                                    temp = ((get_card_swipe_times() * set.cardChargeTime) * set.level3ChargePercentage) / 100;
                                    break;
                                case 3:
                                    temp = ((get_card_swipe_times() * set.cardChargeTime) * set.level4ChargePercentage) / 100;
                                    break;
                                default:
                                    temp = get_card_swipe_times() * set.cardChargeTime;
                                    break;
                                }
                            }
                            else
                                temp = get_card_swipe_times() * set.cardChargeTime;
                            global.increased_charge_time = temp;
                            workStates[x - 1].charge.totalChargeTime = workStates[x - 1].charge.chargeTimes + temp;
                            // 总充电电量
                            temp = get_card_swipe_times() * set.cardMaxPowerConsumption * 10;
                            global.increased_electricity = temp;
                            workStates[x - 1].charge.totalEnergyConsumption = workStates[x - 1].charge.remainBattery + temp;
                            // 总充电金额
                            temp = get_card_swipe_times() * set.cardDeductionAmount;
                            global.increased_card_amount = temp;
                            workStates[x - 1].charge.totalAmount = workStates[x - 1].charge.remainAmount + temp;

                            set_card_swipe_times(0);
                            set_card_last_max_charge_time(0);

                            for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                            {
                                main_view(pdisp, i);
                            }
                            break;
                        default:
                            if (get_local_card_swipe_state() != 0 || get_online_card_swipe_state() != 0)
                            {
                                if (get_local_card_swipe_state() != 0)
                                {
                                    workStates[x - 1].charge.payType = LOCAL_SWIPE_TYPE;
                                }
                                else if (get_online_card_swipe_state() != 0)
                                {
                                    workStates[x - 1].charge.payType = ONLINE_SWIPE_TYPE;
                                }
                                workStates[x - 1].charge.last_card_amount = get_last_card_amount();
                                workStates[x - 1].charge.card_serialNum = get_card_serial_num();

                                workStates[x - 1].charge.totalChargeTime = get_card_swipe_times() * set.cardChargeTime;
                                workStates[x - 1].charge.totalAmount = get_card_swipe_times() * set.cardDeductionAmount;
                                workStates[x - 1].charge.totalEnergyConsumption = get_card_swipe_times() * set.cardMaxPowerConsumption * 10;

                                global.increased_charge_time = workStates[x - 1].charge.totalChargeTime;
                                global.increased_card_amount = workStates[x - 1].charge.totalAmount;
                                global.increased_electricity = workStates[x - 1].charge.totalEnergyConsumption;
                                // 保存刷卡金额
                                if (get_local_card_swipe_state() != 0 && set.freecharge == 0)
                                {
                                    total_card_amount = get_card_totalAmount();
                                    total_card_amount += workStates[x - 1].charge.totalAmount;
                                    if (total_card_amount >= 0xffff)
                                        total_card_amount = 0;
                                    set_card_totalAmount(total_card_amount);
                                    save_sys_parameters(sys_para_eeprom_addr[TOTAL_SYS_PARA_NUM], total_card_amount);
                                }
                                set_card_swipe_times(0);
                                set_card_last_max_charge_time(0);

                                if (get_local_card_swipe_state() != 0)
                                {
                                    power_outage_parameters.local_card_pay_state |= 1 << (x - 1);
                                    power_outage_parameters.online_card_pay_state &= ~(1 << (x - 1));
                                }
                                else if (get_online_card_swipe_state() != 0)
                                {
                                    power_outage_parameters.online_card_pay_state |= 1 << (x - 1);
                                    power_outage_parameters.local_card_pay_state &= ~(1 << (x - 1));
                                }
                                power_outage_parameters.coin_pay_state &= ~(1 << (x - 1));
                                power_outage_parameters.remote_pay_state &= ~(1 << (x - 1));

                                set_local_card_swipe_state(0);
                                set_online_card_swipe_state(0);
                            }
                            else if (coin.isCoin != 0)
                            {
                                coin.isCoin = 0;
                                workStates[x - 1].charge.payType = COIN_TYPE;
                                workStates[x - 1].charge.totalChargeTime = coin.coins * set.coinChargeTime;
                                workStates[x - 1].charge.totalAmount = coin.coins * 10;
                                workStates[x - 1].charge.totalEnergyConsumption = coin.coins * set.coinMaxPowerConsumption * 10;

                                global.increased_charge_time = workStates[x - 1].charge.totalChargeTime;
                                global.increased_coins = workStates[x - 1].charge.totalAmount;
                                global.increased_electricity = workStates[x - 1].charge.totalEnergyConsumption;
                                // 保存投币金额
                                if (set.freecharge == 0)
                                {
                                    coin.totalCoinAmount += workStates[x - 1].charge.totalAmount / 10;
                                    save_sys_parameters(sys_para_eeprom_addr[TOTAL_SYS_PARA_NUM - 1], coin.totalCoinAmount);
                                }

                                coin.coins = 0;
                                coin.max_charge_time = 0;

                                power_outage_parameters.coin_pay_state |= 1 << (x - 1);
                                power_outage_parameters.local_card_pay_state &= ~(1 << (x - 1));
                                power_outage_parameters.online_card_pay_state &= ~(1 << (x - 1));
                                power_outage_parameters.remote_pay_state &= ~(1 << (x - 1));
                            }

                            workStates[x - 1].charge.state = NOMAL_STA;
                            workStates[x - 1].work = NomalChargeState;

                            if (set.freecharge == 0)
                            {
                                if (set.allowedSelfStop != 0)
                                {
                                    set_start_check_charge_port(x - 1, 1);
                                    set_end_check_charge_port(x - 1, 1);
                                    set_check_charge_port_cnt(x - 1, 1);
                                }
                                else
                                {
                                    set_start_check_charge_port(x - 1, 0);
                                    set_end_check_charge_port(x - 1, 0);
                                    set_check_charge_port_cnt(x - 1, 0);
                                }
                                for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                                {
                                    main_view(pdisp, i);
                                }
                            }
                            else
                            {
                                set_start_check_charge_port(x - 1, 0);
                                set_end_check_charge_port(x - 1, 0);
                                set_check_charge_port_cnt(x - 1, 0);
                                set_power_safe_flag(1);
                            }

                            workStates[x - 1].charge.isCharge = 1;
                            workStates[x - 1].charge.check_cnt = 0;

                            power_outage_parameters.port_enable_state |= 1 << (x - 1);

                            set_detect_last_max_power(x - 1, 0);

                            init_charge_rom(x - 1, D_NORMAL_MODE);
                            reset_filter_data(x - 1);

                            relay_write(x - 1, GPIO_PIN_SET);

                            if (set.freecharge == 0)
                            {
                                if (set.allowedSelfStop == 0)
                                    voice_data.type = Voice_Commd_Start_Charge;
                                else
                                    voice_data.type = Voice_Commd_Calculate_Charge_Time;
                            }
                            else
                                voice_data.type = Voice_Commd_Start_Charge;
                            voice_data.power = 0;
                            voice_data.time = 0;
                            voice_data.balance = 0;
                            spell_voice(&voice_data);

                            break;
                        }
                        if (workStates[x - 1].charge.totalChargeTime > 999)
                            workStates[x - 1].charge.totalChargeTime = 999;

                        workStates[x - 1].charge.chargeTimes = workStates[x - 1].charge.totalChargeTime;
                        workStates[x - 1].charge.remainAmount = workStates[x - 1].charge.totalAmount;
                        workStates[x - 1].charge.remainBattery = workStates[x - 1].charge.totalEnergyConsumption;

                        set_dynamic_total_charge_time(x - 1, workStates[x - 1].charge.totalChargeTime);

                        global.lastChargePort = x - 1;
                        global.lastAmount = 0;

                        if (set.freecharge != 0 || set.allowedSelfStop == 0)
                        {
                            for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                            {
                                main_view(pdisp, i);
                            }
                        }
                    }

                    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + ((x - 1) * 2), workStates[x - 1].charge.totalChargeTime);
                    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 20 + ((x - 1) * 2), workStates[x - 1].charge.totalEnergyConsumption);
                    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 40 + ((x - 1) * 2), workStates[x - 1].charge.totalAmount);

                    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 60 + ((x - 1) * 2), workStates[x - 1].charge.chargeTimes);
                    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 80 + ((x - 1) * 2), workStates[x - 1].charge.remainBattery);
                    save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 100 + ((x - 1) * 2), workStates[x - 1].charge.remainAmount);

                    save_parameters(OUTAGE_FLAG_16BIT_ADDR, power_outage_parameters.port_enable_state);
                    save_parameters(OUTAGE_FLAG_16BIT_ADDR + 2, power_outage_parameters.local_card_pay_state);
                    save_parameters(OUTAGE_FLAG_16BIT_ADDR + 4, power_outage_parameters.online_card_pay_state);
                    save_parameters(OUTAGE_FLAG_16BIT_ADDR + 6, power_outage_parameters.coin_pay_state);
                    save_parameters(OUTAGE_FLAG_16BIT_ADDR + 8, power_outage_parameters.remote_pay_state);

                    if (send_flag == 0 && set.freecharge == 0)
                    {
                        set_upload_all_timer(0);
                        send_gprs_mb_cmd(0x20, x);
                    }
                }
            }
        }
        else if (workStates[x - 1].charge.isCharge != 0 && clear.isClear == 0)
        {
            switch (++workStates[x - 1].charge.check_cnt)
            {
            case 1:
                voice_cmmd = Voice_Commd_Charge_Power;

                pdisp->type = Parameters;
                pdisp->data = workStates[x - 1].charge.chargePower;
                pdisp->hide = No_Hide;

                if (workStates[x - 1].charge.chargePower >= 1000)
                {
                    pdisp->point = Pos_2;
                    pdisp->data /= 10;
                }
                else
                {
                    pdisp->point = No_Point;
                }

                set_start_check_charge_port(x - 1, 0);

                break;
            case 2:
                if (set.initDisplay != 0)
                {
                    voice_cmmd = Voice_Commd_Charge_Time;

                    if (workStates[x - 1].charge.isNoConnect != 0 || workStates[x - 1].charge.isHighPower != 0)
                    {
                        if (global.flip != 0)
                            pdisp->hide = Hidden_All;
                        else
                            pdisp->hide = No_Hide;

                        pdisp->point = No_Point;
                        pdisp->type = Error;

                        if (workStates[x - 1].charge.isNoConnect != 0)
                            pdisp->data = DISP_ERROR_NUM;
                        else if (workStates[x - 1].charge.isHighPower != 0)
                            pdisp->data = DISP_HIGH_NUM;
                    }
                    else
                    {
                        if (global.flip == 0)
                            pdisp->point = Pos_0;
                        else
                            pdisp->point = No_Point;

                        if (get_start_check_charge_port(x - 1) != 0)
                            pdisp->type = Check_power;
                        else
                            pdisp->type = Parameters;
                        pdisp->data = workStates[x - 1].charge.chargeTimes;
                        pdisp->hide = No_Hide;
                    }
                }
                else
                {
                    voice_cmmd = Voice_Commd_Remaining_Battery;

                    pdisp->type = Parameters;
                    pdisp->hide = No_Hide;

                    if (workStates[x - 1].charge.remainBattery > 9999)
                    {
                        pdisp->point = No_Point;
                        pdisp->data = workStates[x - 1].charge.remainBattery / 100;
                    }
                    else
                    {
                        if (workStates[x - 1].charge.remainBattery > 999)
                        {
                            pdisp->point = Pos_1;
                            pdisp->data = workStates[x - 1].charge.remainBattery / 10;
                        }
                        else
                        {
                            pdisp->point = Pos_2;
                            pdisp->data = workStates[x - 1].charge.remainBattery;
                        }
                    }
                    if (set.allowedSelfStop == 0 || set.freecharge != 0)
                        pdisp->data = 0;

                    set_start_check_charge_port(x - 1, 0);
                }
                break;
            case 3:
                voice_cmmd = Voice_Commd_Balance;

                pdisp->type = Parameters;
                pdisp->hide = No_Hide;

                if (workStates[x - 1].charge.payType != NO_TYPE)
                {
                    if (workStates[x - 1].charge.payType == COIN_TYPE)
                    {
                        pdisp->point = No_Point;
                        pdisp->data = 0;
                    }
                    else
                    {
                        if (workStates[x - 1].charge.remainAmount > 999)
                        {
                            pdisp->point = No_Point;
                            pdisp->data = workStates[x - 1].charge.remainAmount / 10;
                        }
                        else
                        {
                            pdisp->point = Pos_1;
                            pdisp->data = workStates[x - 1].charge.remainAmount;
                        }
                    }
                }

                set_start_check_charge_port(x - 1, 0);
                break;
            default:
                workStates[x - 1].charge.check_cnt = 0;
                if (set.initDisplay != 0)
                {
                    voice_cmmd = Voice_Commd_Remaining_Battery;

                    pdisp->type = Parameters;
                    pdisp->hide = No_Hide;

                    if (workStates[x - 1].charge.remainBattery > 9999)
                    {
                        pdisp->point = No_Point;
                        pdisp->data = workStates[x - 1].charge.remainBattery / 100;
                    }
                    else
                    {
                        if (workStates[x - 1].charge.remainBattery > 999)
                        {
                            pdisp->point = Pos_1;
                            pdisp->data = workStates[x - 1].charge.remainBattery / 10;
                        }
                        else
                        {
                            pdisp->point = Pos_2;
                            pdisp->data = workStates[x - 1].charge.remainBattery;
                        }
                    }
                    if (set.allowedSelfStop == 0 || set.freecharge != 0)
                        pdisp->data = 0;

                    set_start_check_charge_port(x - 1, 0);
                }
                else
                {
                    voice_cmmd = Voice_Commd_Charge_Time;

                    if (workStates[x - 1].charge.isNoConnect != 0 || workStates[x - 1].charge.isHighPower != 0)
                    {
                        if (global.flip != 0)
                            pdisp->hide = Hidden_All;
                        else
                            pdisp->hide = No_Hide;

                        pdisp->point = No_Point;
                        pdisp->type = Error;

                        if (workStates[x - 1].charge.isNoConnect != 0)
                            pdisp->data = DISP_ERROR_NUM;
                        else if (workStates[x - 1].charge.isHighPower != 0)
                            pdisp->data = DISP_HIGH_NUM;
                    }
                    else
                    {
                        if (global.flip == 0)
                            pdisp->point = Pos_0;
                        else
                            pdisp->point = No_Point;

                        if (get_start_check_charge_port(x - 1) != 0)
                            pdisp->type = Check_power;
                        else
                            pdisp->type = Parameters;
                        pdisp->data = workStates[x - 1].charge.chargeTimes;
                        pdisp->hide = No_Hide;
                    }
                }
                break;
            }

            pdisp->addr = x - 1;
            auto_show_digitaltube(pdisp);

            //            global.stop_disp_flag = 1;

            if (workStates[x - 1].charge.check_cnt != 0)
                workStates[x - 1].charge.back_cnt = 20;
            else
                workStates[x - 1].charge.back_cnt = 0;

            //            speeker(voice_cmmd);
            voice_data.type = voice_cmmd;
            voice_data.power = 0;
            voice_data.time = 0;
            voice_data.balance = 0;
            spell_voice(&voice_data);
        }
    }
}

// 进入参数清零界面
static void clear_view(struct disp_t *pdisp)
{
    uint8_t i = 0;
    WorkState state;

    for (i = 0; i < TOTAL_PORTS_NUM; i++)
    {
        switch (workStates[i].charge.state)
        {
        case LOCK_STA:
            pdisp->type = Error;
            pdisp->data = DISP_ERROR_NUM;
            state = NoState;
            break;
        case HIGH_STA:
            pdisp->type = Error;
            pdisp->data = DISP_HIGH_NUM;
            state = NoState;
            break;
        case NOMAL_STA:
            pdisp->type = Parameters;
            state = ClearState;
            if (test_tab[i].isTest != 0)
            {
                pdisp->data = test.time;
            }
            else if (workStates[i].charge.isCharge != 0)
            {
                pdisp->data = workStates[i].charge.chargeTimes;
            }
            else if (test_tab[i].isConnect != 0)
            {
                pdisp->type = Error;
                pdisp->data = DISP_LINE_NUM;
                state = NoState;
            }
            break;
        case ERROR_STA:
            pdisp->type = Error;
            pdisp->data = DISP_ERROR_NUM;
            state = NoState;
            break;
        default:
            pdisp->type = Error;
            pdisp->data = DISP_LINE_NUM;
            state = NoState;
            break;
        }

        workStates[i].work = state;

        pdisp->addr = i;
        pdisp->point = No_Point;
        pdisp->hide = No_Hide;
        auto_show_digitaltube(pdisp);
    }
}

// 参数清零模式
static void clear_fun(struct disp_t *pdisp, uint8_t x)
{
    if (clear.isClear != 0)
    {
        if (workStates[x - 1].charge.isCharge != 0)
        {
            pdisp->type = Error;
            pdisp->data = DISP_LINE_NUM;

            before_stop_remain_time = workStates[x - 1].charge.chargeTimes;
            before_stop_remain_battery = workStates[x - 1].charge.remainBattery;
            stop_reason = FAULT_STOP;
            stop_charge_port(x - 1);

            if (workStates[x - 1].charge.payType == ONLINE_SWIPE_TYPE || workStates[x - 1].charge.payType == REMOTE_TYPE)
            {
                if (set.freecharge == 0)
                {
                    set_upload_all_timer(0);
                    send_gprs_mb_cmd(0x16, x - 1);
                }
            }
        }
        else if (test_tab[x - 1].isTest != 0)
        {
            test_tab[x - 1].isTest = 0;
            workStates[x - 1].work = NoState;
            workStates[x - 1].charge.state = IDLE_STA;

            test.time = 0;
            test.last_port = 0;

            pdisp->type = Error;
            pdisp->data = DISP_LINE_NUM;

            relay_write(x - 1, GPIO_PIN_RESET);
        }
        else if (workStates[x - 1].charge.state == HIGH_STA)
        {
            pdisp->type = Error;
            pdisp->data = DISP_LINE_NUM;

            before_stop_remain_time = workStates[x - 1].charge.chargeTimes;
            before_stop_remain_battery = workStates[x - 1].charge.remainBattery;
            stop_reason = FAULT_STOP;
            stop_charge_port(x - 1);
            if (workStates[x - 1].charge.payType == ONLINE_SWIPE_TYPE || workStates[x - 1].charge.payType == REMOTE_TYPE)
            {
                set_upload_all_timer(0);
                send_gprs_mb_cmd(0x16, x - 1);
            }
        }
        else
        {
            if (workStates[x - 1].charge.state == LOCK_STA || workStates[x - 1].charge.state == ERROR_STA)
            {
                if (set.freecharge == 0)
                {
                    COIN_ENABLE;
                    set_local_card_state(0);
                    set_online_card_state(0);
                }
                else
                {
                    COIN_DISABLE;
                    set_local_card_state(1);
                    set_online_card_state(1);
                }

                pdisp->type = Error;
                pdisp->data = DISP_LINE_NUM;
                workStates[x - 1].charge.state = IDLE_STA;
                power_outage_parameters.port_lock_state &= ~(1 << (x - 1));
            }
            else
            {
                pdisp->type = Error;
                pdisp->data = DISP_ERROR_NUM;
                workStates[x - 1].charge.state = LOCK_STA;

                power_outage_parameters.port_lock_state |= (1 << (x - 1));
            }
            save_parameters(OUTAGE_FLAG_16BIT_ADDR + 10, power_outage_parameters.port_lock_state);
        }

        pdisp->addr = x - 1;
        pdisp->point = No_Point;
        pdisp->hide = No_Hide;
        auto_show_digitaltube(pdisp);

        init_charge_rom(x - 1, D_NORMAL_MODE);
        reset_filter_data(x - 1);
    }
}

static void clear_total_record_money(struct disp_t *pdisp)
{
    rw_sys_parameters(WRITE, sizeof(SetTitle) - 2, 0);
    rw_sys_parameters(WRITE, sizeof(SetTitle) - 3, 0);

    pdisp->type = Check_Amount;
    pdisp->hide = No_Hide;
    pdisp->point = Pos_4;
    pdisp->data = 0;
    pdisp->addr = 1;
    auto_show_digitaltube(pdisp);

    pdisp->type = Set;
    pdisp->point = No_Point;
    pdisp->hide = No_Hide;
    pdisp->addr = 0;
    pdisp->data = SetTitle[sizeof(SetTitle) - 1];
    auto_show_digitaltube(pdisp);

    save_sys_parameters(sys_para_eeprom_addr[TOTAL_SYS_PARA_NUM - 1], 0);
    save_sys_parameters(sys_para_eeprom_addr[TOTAL_SYS_PARA_NUM], 0);
}

// 参数设置菜单
static void set_menu(struct disp_t *pdisp)
{
    t_voice_data voice_data;

    uint8_t i = 0;

    if ((set.isSet != 0 && set.pos > 27 && set.pos != 0xff) || (calibrate.isCalibrate != 0 && calibrate.isCalibrating == 0) || clear.isClear != 0)
    {
        set.pos = 0;
        clear.isClear = 0;
        global.stop_disp_flag = 0;
        global.back_timer = 0;

        COIN_ENABLE;
        set_local_card_state(0);
        set_online_card_state(0);

        if (set.isSet != 0)
        {
            set.isSet = 0;

            uint8_t tx_buf[5];

            tx_buf[0] = 0xAA;
            tx_buf[1] = 0x88;
            tx_buf[2] = 0x95;
            tx_buf[3] = 0xdd;
            tx_buf[4] = chk_xrl(tx_buf, 4);
            // 发送数据
            uart_send_data(&huart2, tx_buf, 5);
        }
        else if (calibrate.isCalibrate != 0)
        {
            calibrate.isCalibrate = 0;
            calibrate.page_cnt = 0;
            calibrate.isRefPowerAdjust = 0;
        }

        for (i = 0; i < TOTAL_PORTS_NUM; i++)
        {
            if (test_tab[i].isTest != 0 && test.time != 0)
            {
                workStates[i].work = TestState;
                workStates[i].charge.state = NOMAL_STA;
            }
            else if (workStates[i].charge.isCharge != 0) // && workStates[i].charge.chargeTimes != 0)
            {
                workStates[i].work = NomalChargeState;
                //                workStates[i].charge.state = NOMAL_STA;
            }
        }

        //        speeker(Voice_Commd_Exit_Setmode);
        voice_data.type = Voice_Commd_Exit_Setmode;
        voice_data.power = 0;
        voice_data.time = 0;
        voice_data.balance = 0;
        spell_voice(&voice_data);

        for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
        {
            main_view(pdisp, i);
        }
    }
    else
    {
        if (calibrate.page_cnt == 0 && calibrate.isCalibrate == 0 && calibrate.isCalibrating == 0)
        {
            if (set.isSet == 0)
            {
                seg_clear();
                set.pos = 0;

                COIN_DISABLE;
                set_local_card_state(1);
                set_online_card_state(1);
            }

            set.isSet = 1;
            global.stop_disp_flag = 1;
            global.back_timer = 30;

            if (set.pos == 0)
            {
                set.pos = 0xff;

                pdisp->type = Check_Version;
                pdisp->point = Pos_5;
                pdisp->hide = No_Hide;
                pdisp->addr = 0;
                pdisp->data = VERSION;
                auto_show_digitaltube(pdisp);

                return;
            }
            else if (set.pos == 0xff)
            {
                set.pos = 0;
                seg_clear();
            }

            if (set.pos <= 14)
            {
                if (set.pos == 13)
                    voice_data.type = Voice_Commd_Set_Pay_Method;
                else if (set.pos == 14)
                    voice_data.type = Voice_Commd_Support_FreeMode;
                else
                    voice_data.type = (set.pos) + Voice_Commd_Set_Coin_Charge_Time;
            }
            else if (set.pos > 14 && set.pos <= 20)
            {
                if (set.pos == 19)
                    voice_data.type = Voice_Commd_Set_Max_Temperture;
                else if (set.pos == 20)
                    voice_data.type = Voice_Commd_Set_Max_Total_Power;
                else
                    voice_data.type = (set.pos - 2) + Voice_Commd_Set_Coin_Charge_Time;
            }
            else if (set.pos == 21 || set.pos == 22)
            {
                voice_data.type = 0;
            }
            else if (set.pos >= 23)
            {
                if (set.pos == 23)
                    voice_data.type = Voice_Commd_Current_Temp;
                else if (set.pos >= 24)
                    voice_data.type = (set.pos - 7) + Voice_Commd_Set_Coin_Charge_Time;
            }
            voice_data.power = 0;
            voice_data.time = 0;
            voice_data.balance = 0;
            spell_voice(&voice_data);

            if (set.pos == 27)
            {
                seg_clear();
                clear_view(pdisp);
                clear.isClear = 1;
            }
            else
            {
                if (set.pos <= 11 || set.pos == 16 || set.pos == 17 || set.pos == 19 || set.pos == 21 || set.pos == 22)
                {
                    if (set.pos == 2 || set.pos == 3 || set.pos == 4)
                        pdisp->point = Pos_1;
                    else
                        pdisp->point = No_Point;
                    pdisp->type = Parameters;
                    pdisp->hide = No_Hide;
                    pdisp->data = rw_sys_parameters(READ, set.pos, 0);
                    if (pdisp->data >= 1000)
                    {
                        pdisp->point = Pos_2;
                        pdisp->data /= 10;
                    }
                }
                else if (set.pos == 12 || set.pos == 13 || set.pos == 14 || set.pos == 15 || set.pos == 18)
                {
                    pdisp->type = Parameters;
                    pdisp->point = No_Point;
                    pdisp->hide = Hidden_Pos_2and3;
                    pdisp->data = rw_sys_parameters(READ, set.pos, 0);
                }
                else if (set.pos == 20)
                {
                    pdisp->type = Parameters;
                    pdisp->point = Pos_1;
                    pdisp->hide = No_Hide;
                    pdisp->data = rw_sys_parameters(READ, set.pos, 0);
                }
                else if (set.pos == 23)
                {
                    pdisp->type = Check_temperture;
                    pdisp->point = No_Point;
                    pdisp->hide = No_Hide;
                    pdisp->data = rw_sys_parameters(READ, set.pos, 0);
                }
                else if (set.pos >= 24 && set.pos <= 26)
                {
                    pdisp->type = Check_Amount;
                    pdisp->hide = No_Hide;
                    if (set.pos == 25 || set.pos == 26)
                        pdisp->point = Pos_4;
                    else
                        pdisp->point = No_Point;

                    if (set.pos == 26)
                    {
                        pdisp->data = rw_sys_parameters(READ, set.pos - 2, 0) * 10 + rw_sys_parameters(READ, set.pos - 1, 0);
                        if (pdisp->data > 999999 && pdisp->data < 0x1FFFE)
                        {
                            pdisp->point = No_Point;
                            pdisp->data /= 10;
                        }
                        else if (pdisp->data >= 0x1FFFE)
                        {
                            pdisp->point = Pos_4;
                            pdisp->data = 0;
                        }
                    }
                    else
                        pdisp->data = rw_sys_parameters(READ, set.pos, 0);
                }
                pdisp->addr = 1;
                auto_show_digitaltube(pdisp);

                pdisp->type = Set;
                pdisp->point = No_Point;
                pdisp->hide = No_Hide;
                pdisp->addr = 0;
                pdisp->data = SetTitle[set.pos];
                auto_show_digitaltube(pdisp);
            }
            set.pos++;
        }
    }
}

// 设置模式,参数增
static void set_add_fun(struct disp_t *pdisp)
{
    t_voice_data voice_data;
    uint8_t i = 0;
    uint16_t add;

    global.stop_disp_flag = 1;
    global.back_timer = 30;

    if (set.isSet != 0)
    {
        if ((set.pos - 1) < 23)
        {
            if ((set.pos - 1) == 12 || (set.pos - 1) == 13 || (set.pos - 1) == 14 || (set.pos - 1) == 15 || (set.pos - 1) == 18)
            {
                pdisp->point = No_Point;
                pdisp->hide = Hidden_Pos_2and3;

                if (rw_sys_parameters(READ, set.pos - 1, 0) != 0)
                {
                    add = 0;
                    if ((set.pos - 1) == 12)
                        set.allowedRecovery = 0;
                    else if ((set.pos - 1) == 13)
                        set.billing_method = 0;
                    else if ((set.pos - 1) == 14)
                        set.freecharge = 0;
                    else if ((set.pos - 1) == 15)
                    {
                        set.allowedSelfStop = 0;
                        for (i = 0; i < TOTAL_PORTS_NUM; i++)
                        {
                            set_stop_recal_flag(i, 0);
                        }
                    }
                    else
                        set.initDisplay = 0;
                }
                else
                {
                    add = 1;
                    if ((set.pos - 1) == 12)
                        set.allowedRecovery = 1;
                    else if ((set.pos - 1) == 13)
                        set.billing_method = 1;
                    else if ((set.pos - 1) == 14)
                        set.freecharge = 1;
                    else if ((set.pos - 1) == 15)
                    {
                        set.allowedSelfStop = 1;
                        for (i = 0; i < TOTAL_PORTS_NUM; i++)
                        {
                            if (get_charge_state(i) != 0)
                            {
                                set_stop_recal_flag(i, 1);
                            }
                        }
                    }
                    else
                        set.initDisplay = 1;
                }
            }
            else
            {
                if ((set.pos - 1) == 2 || (set.pos - 1) == 3 || (set.pos - 1) == 4 || (set.pos - 1) == 20)
                    pdisp->point = Pos_1;
                else
                    pdisp->point = No_Point;

                pdisp->hide = No_Hide;

                add = rw_sys_parameters(READ, set.pos - 1, 0);
                add += adddata[set.pos - 1];
                if (add > limitdata[set.pos - 1])
                    add = 0;
            }

            rw_sys_parameters(WRITE, set.pos - 1, add);
            // 保存变更的参数
            save_sys_parameters(sys_para_eeprom_addr[set.pos - 1], add);

            if (add >= 1000)
            {
                pdisp->point = Pos_2;
                add /= 10;
            }

            pdisp->addr = 1;
            pdisp->type = Parameters;
            pdisp->data = add;
            auto_show_digitaltube(pdisp);
        }
        else
        {
            if ((set.pos - 1) == 26)
            {
                clear_total_record_money(pdisp);
            }
        }
    }
    else
    {
        // 进入充电清零界面
        if (clear.isClear == 0 && calibrate.isCalibrate == 0 && calibrate.isCalibrating == 0)
        {
            COIN_DISABLE;
            set_local_card_state(1);
            set_online_card_state(1);
            set.pos = 0;
            clear.isClear = 1;
            global.stop_disp_flag = 1;
            global.back_timer = 30;
            //            speeker(Voice_Commd_Clear_Charge_Time);
            voice_data.type = Voice_Commd_Clear_Charge_Time;
            voice_data.power = 0;
            voice_data.time = 0;
            voice_data.balance = 0;
            spell_voice(&voice_data);

            clear_view(pdisp);
        }
    }
}

// 设置模式,参数减
static void set_sub_fun(struct disp_t *pdisp)
{
    t_voice_data voice_data;
    int sub;
    uint8_t i = 0;

    if (set.isSet != 0 && calibrate.isCalibrate == 0)
    {
        global.stop_disp_flag = 1;
        global.back_timer = 30;

        if ((set.pos - 1) < 23)
        {
            if ((set.pos - 1) == 12 || (set.pos - 1) == 13 || (set.pos - 1) == 14 || (set.pos - 1) == 15 || (set.pos - 1) == 18)
            {
                pdisp->point = No_Point;
                pdisp->hide = Hidden_Pos_2and3;

                if (rw_sys_parameters(READ, set.pos - 1, 0) != 0)
                {
                    sub = 0;
                    if ((set.pos - 1) == 12)
                        set.allowedRecovery = 0;
                    else if ((set.pos - 1) == 13)
                        set.billing_method = 0;
                    else if ((set.pos - 1) == 14)
                        set.freecharge = 0;
                    else if ((set.pos - 1) == 15)
                    {
                        set.allowedSelfStop = 0;
                        for (i = 0; i < TOTAL_PORTS_NUM; i++)
                        {
                            set_stop_recal_flag(i, 0);
                        }
                    }
                    else
                        set.initDisplay = 0;
                }
                else
                {
                    sub = 1;
                    if ((set.pos - 1) == 12)
                        set.allowedRecovery = 1;
                    else if ((set.pos - 1) == 13)
                        set.billing_method = 1;
                    else if ((set.pos - 1) == 14)
                        set.freecharge = 1;
                    else if ((set.pos - 1) == 15)
                    {
                        set.allowedSelfStop = 1;
                        for (i = 0; i < TOTAL_PORTS_NUM; i++)
                        {
                            if (get_charge_state(i) != 0)
                            {
                                set_stop_recal_flag(i, 1);
                            }
                        }
                    }
                    else
                        set.initDisplay = 1;
                }
            }
            else
            {
                if ((set.pos - 1) == 2 || (set.pos - 1) == 3 || (set.pos - 1) == 4 || (set.pos - 1) == 20)
                    pdisp->point = Pos_1;
                else
                    pdisp->point = No_Point;

                pdisp->hide = No_Hide;

                sub = rw_sys_parameters(READ, set.pos - 1, 0);
                if (sub > 1000)
                    sub -= 10;
                else
                    sub -= subdata[set.pos - 1];

                if (sub <= 0)
                    sub = limitdata[set.pos - 1];
            }

            rw_sys_parameters(WRITE, set.pos - 1, sub);
            // 保存变更的参数
            save_sys_parameters(sys_para_eeprom_addr[set.pos - 1], sub);

            if (sub >= 1000)
            {
                pdisp->point = Pos_2;
                sub /= 10;
            }

            pdisp->addr = 1;
            pdisp->type = Parameters;
            pdisp->data = sub;
            auto_show_digitaltube(pdisp);
        }
        else
        {
            if ((set.pos - 1) == 26)
            {
                clear_total_record_money(pdisp);
            }
        }
    }
    else
    {
        // 进入校准界面
        if (clear.isClear == 0)
        {
            global.stop_disp_flag = 0;
            global.back_timer = 0;
            calibrate.isCalibrate = 1;

            COIN_DISABLE;
            set_local_card_state(1);
            set_online_card_state(1);

            seg_clear();
            //            speeker(Voice_Commd_Calibrate_Power);
            voice_data.type = Voice_Commd_Calibrate_Power;
            voice_data.power = 0;
            voice_data.time = 0;
            voice_data.balance = 0;
            spell_voice(&voice_data);

            calibrate.page_cnt++;
            if (calibrate.page_cnt == 1)
            {
                calibrate.isRefPowerAdjust = 1;
                if (calibrate.ref_P <= 999)
                {
                    pdisp->data = calibrate.ref_P;
                    pdisp->point = No_Point;
                }
                else if (calibrate.ref_P > 999 && calibrate.ref_P <= 9999)
                {
                    pdisp->data = calibrate.ref_P / 10;
                    pdisp->point = Pos_2;
                }
                else
                {
                    pdisp->data = calibrate.ref_P / 100;
                    pdisp->point = Pos_1;
                }
                pdisp->type = Parameters;
                pdisp->addr = 0;
                pdisp->hide = No_Hide;
                auto_show_digitaltube(pdisp);
            }
            else if (calibrate.page_cnt == 2)
            {
                calibrate.page_cnt = 0;
                calibrate.isRefPowerAdjust = 0;

                for (i = 0; i < TOTAL_PORTS_NUM; i++)
                {
                    if (calibrate.ref_F[i] >= 10000)
                        pdisp->data = calibrate.ref_F[i] / 100;
                    else
                        pdisp->data = calibrate.ref_F[i] / 10;

                    pdisp->type = Parameters;
                    pdisp->addr = i;
                    pdisp->point = No_Point;
                    pdisp->hide = No_Hide;
                    auto_show_digitaltube(pdisp);
                }
            }
        }
    }
}

// 校准模式
static void calibrate_fun(uint8_t x)
{
    if (calibrate.isCalibrate != 0 && calibrate.isCalibrating == 0 && workStates[x - 1].charge.isCharge == 0)
    {
        calibrate.isCalibrating = 1;
        workStates[x - 1].work = CalibrateState;
        workStates[x - 1].isAjustState = 1;

        init_charge_rom(x - 1, D_CAL_START_MODE);
        reset_filter_data(x - 1);

        relay_write(x - 1, GPIO_PIN_SET);
    }
}

// 校准模式,参数增
static void calibrate_add_fun(struct disp_t *pdisp)
{
    if (calibrate.ref_P <= 999)
        calibrate.ref_P += 5;
    else
        calibrate.ref_P += 10;

    if (calibrate.ref_P > 5000)
        calibrate.ref_P = 0;

    if (calibrate.ref_P <= 999)
    {
        pdisp->data = calibrate.ref_P;
        pdisp->point = No_Point;
    }
    else if (calibrate.ref_P > 999 && calibrate.ref_P <= 9999)
    {
        pdisp->data = calibrate.ref_P / 10;
        pdisp->point = Pos_2;
    }
    else
    {
        pdisp->data = calibrate.ref_P / 100;
        pdisp->point = Pos_1;
    }
    // 保存参考功率
    save_parameters(REF_PARA_16BIT_ADDR + 2, calibrate.ref_P);

    pdisp->type = Parameters;
    pdisp->addr = 0;
    //    pdisp->data = calibrate.ref_P;
    //    pdisp->point = No_Point;
    pdisp->hide = No_Hide;
    auto_show_digitaltube(pdisp);
}

// 校准模式,参数减
static void calibrate_sub_fun(struct disp_t *pdisp)
{
    int sub = 0;

    sub = calibrate.ref_P;
    if (calibrate.ref_P <= 999)
        sub -= 1;
    else
        sub -= 10;

    if (sub < 0)
        sub = 5000;

    calibrate.ref_P = sub;

    if (calibrate.ref_P <= 999)
    {
        pdisp->data = calibrate.ref_P;
        pdisp->point = No_Point;
    }
    else if (calibrate.ref_P > 999 && calibrate.ref_P <= 9999)
    {
        pdisp->data = calibrate.ref_P / 10;
        pdisp->point = Pos_2;
    }
    else
    {
        pdisp->data = calibrate.ref_P / 100;
        pdisp->point = Pos_1;
    }
    // 保存参考功率
    save_parameters(REF_PARA_16BIT_ADDR + 2, calibrate.ref_P);

    pdisp->type = Parameters;
    pdisp->addr = 0;
    //    pdisp->data = calibrate.ref_P;
    //    pdisp->point = No_Point;
    pdisp->hide = No_Hide;
    auto_show_digitaltube(pdisp);
}

// 校准回调函数
static void calibrate_timer_callback(struct disp_t *pdisp, uint8_t x)
{
    uint32_t temp;
    static uint8_t dir = 0;
    static uint8_t point_pos = 0;

    if (calibrate.isCalibrateComplete != 0)
    {
        dir = 0;
        point_pos = 0;
        calibrate.isCalibrateComplete = 0;
        calibrate.isCalibrating = 0;
        workStates[x].isAjustState = 0;
        workStates[x].work = NoState;

        if (get_power_last_testonecycle_mode(x) != 0)
        {
            temp = get_power_last_onecycle_time(x);
            temp *= 1000;
        }
        else
        {
            temp = get_power_last_onecycle_time(x);
            temp *= 1000;
            temp /= (get_power_last_cnt(x) - 1);
        }

        if (temp > 0xffff)
        {
            calibrate.isOver |= (1 << x);
            calibrate.ref_F[x] = temp - 0xffff;
        }
        else
        {
            calibrate.isOver &= ~(1 << x);
            calibrate.ref_F[x] = temp;
        }

        // 保存参考功率频率,参考电量频率,溢出标志位
        save_parameters(REF_PARA_16BIT_ADDR, calibrate.isOver);
        save_parameters((x * 2) + REF_PARA_16BIT_ADDR + 4, calibrate.ref_F[x]);
        save_parameters((x * 2) + REF_PARA_16BIT_ADDR + 24, calibrate.ref_E[x]);

        init_charge_rom(x, D_NORMAL_MODE);
        reset_filter_data(x);

        relay_write(x, GPIO_PIN_RESET);

        if (calibrate.ref_F[x] >= 10000)
            pdisp->data = calibrate.ref_F[x] / 100;
        else
            pdisp->data = calibrate.ref_F[x] / 10;

        pdisp->type = Parameters;
        pdisp->addr = x;
        pdisp->point = No_Point;
        pdisp->hide = No_Hide;
        auto_show_digitaltube(pdisp);
    }
    else
    {
        pdisp->type = Parameters;
        pdisp->addr = x;
        pdisp->data = 0;
        pdisp->point = point_pos;
        pdisp->hide = Hidden_All;
        auto_show_digitaltube(pdisp);

        if (dir == 0)
        {
            if (++point_pos >= 2)
                dir = 1;
        }
        else
        {
            if (--point_pos == 0)
                dir = 0;
        }
    }
}

// 测试时间倒计时回调函数
static void test_timer_callback(struct disp_t *pdisp, uint8_t x)
{
    t_voice_data voice_data;

    if (global.flip != 0)
        pdisp->hide = Hidden_All;
    else
        pdisp->hide = No_Hide;

    if (test_tab[x].isConnect != 0)
    {
        if (++test.flash_cnt >= 20)
        {
            test.flash_cnt = 0;
            pdisp->hide = No_Hide;
            workStates[x].work = NoState;
            workStates[x].charge.state = IDLE_STA;
            test_tab[x].isTest = 0;
            test_tab[x].isConnect = 0;
            test.last_port = 0;
        }
    }
    else if (test_tab[x].isTest != 0)
    {
        if (--test.time == 0)
        {
            pdisp->hide = No_Hide;
            test.last_port = 0;
            test_tab[x].isTest = 0;
            workStates[x].work = NoState;
            workStates[x].charge.state = IDLE_STA;

            relay_write(x, GPIO_PIN_RESET);

            init_charge_rom(x, D_NORMAL_MODE);
            reset_filter_data(x);

            if (test_tab[x].isConnect == 0)
            {
                //                speeker(Voice_Commd_Please_Reconnect);
                voice_data.type = Voice_Commd_Please_Reconnect;
                voice_data.power = 0;
                voice_data.time = 0;
                voice_data.balance = 0;
                spell_voice(&voice_data);
            }
        }
    }

    if (global.stop_disp_flag == 0)
    {
        pdisp->type = Idle;
        pdisp->addr = x;
        pdisp->data = x + 1;
        pdisp->point = No_Point;
        auto_show_digitaltube(pdisp);
    }
}

// 在发生故障，多次检测完成后根据不同的消费方式，返回不同的界面
static void fault_back_callback(struct disp_t *pdisp, uint8_t x)
{
    t_voice_data voice_data;

    uint16_t temp = 0;
    Consume pay_type;

    COIN_ENABLE;
    set_local_card_state(0);
    set_online_card_state(0);

    set_detect_state(x, 0);

    voice_data.type = Voice_Commd_Charge_Socket_Fauty;

    // 没有负载连接的情况下
    if (workStates[x].charge.isReconnect != 0)
    {
        global.isErrorSta = 1;
        global.reChoosePort = 1;
        global.stop_disp_flag = 1;
        pdisp->data = workStates[x].charge.remainAmount;

        pay_type = workStates[x].charge.payType;
        switch (pay_type)
        {
        case COIN_TYPE:
            if (set.freecharge == 0)
            {
                global.back_timer = 10;
                seg_clear();
                if (pdisp->data > 9999)
                    pdisp->data = 9999;
                pdisp->data /= 10;
                pdisp->type = Parameters;
                pdisp->addr = 0;
                pdisp->point = No_Point;
                pdisp->hide = No_Hide;
            }
            else
            {
                pdisp->type = Idle;
                pdisp->addr = x;
                pdisp->data = x + 1;
                pdisp->point = No_Point;
                pdisp->hide = No_Hide;

                global.stop_disp_flag = 0;
                global.back_timer = 0;
                set_rechoose_state(0);
                stop_charge_port(x);
            }
            auto_show_digitaltube(pdisp);
            break;
        case LOCAL_SWIPE_TYPE:
            //            global.back_timer = 10;
            //            seg_clear();
            //            if (pdisp->data < 1000)
            //                pdisp->point = Pos_1;
            //            else
            //            {
            //                pdisp->point = No_Point;
            //                pdisp->data /= 10;
            //            }
            //            pdisp->type = Parameters;
            //            pdisp->addr = 0;
            //            pdisp->hide = No_Hide;
            //            auto_show_digitaltube(pdisp);

            //            if (get_card_balance() < 1000)
            //            {
            //                pdisp->point = Pos_1;
            //                pdisp->data = get_card_balance();
            //            }
            //            else
            //            {
            //                pdisp->point = No_Point;
            //                pdisp->data = get_card_balance() / 10;
            //            }
            //            pdisp->type = Parameters;
            //            pdisp->addr = 1;
            //            pdisp->hide = No_Hide;
            //            auto_show_digitaltube(pdisp);

            voice_data.type = 0;

            pdisp->type = Idle;
            pdisp->addr = x;
            pdisp->data = x + 1;
            pdisp->point = No_Point;
            pdisp->hide = No_Hide;
            auto_show_digitaltube(pdisp);

            global.stop_disp_flag = 0;
            global.back_timer = 0;
            set_rechoose_state(0);

            stop_charge_port(x);
            break;
        case ONLINE_SWIPE_TYPE:
            pdisp->type = Idle;
            pdisp->addr = x;
            pdisp->data = x + 1;
            pdisp->point = No_Point;
            pdisp->hide = No_Hide;
            auto_show_digitaltube(pdisp);

            global.stop_disp_flag = 0;
            global.back_timer = 0;
            set_rechoose_state(0);
            set_before_stop_remain_time(get_charge_times(x));
            set_before_stop_remain_battery(get_charge_remain_battery(x));
            set_charge_port_stop_reason(FAULT_STOP);

            set_upload_all_timer(0);
            send_gprs_mb_cmd(0x16, x);

            stop_charge_port(x);
            break;
        case REMOTE_TYPE:
            pdisp->type = Idle;
            pdisp->addr = x;
            pdisp->data = x + 1;
            pdisp->point = No_Point;
            pdisp->hide = No_Hide;
            auto_show_digitaltube(pdisp);

            set_rechoose_state(0);
            global.back_timer = 0;
            global.stop_disp_flag = 0;
            stop_charge_port(x);

            set_remote_charge_state(0x0B);
            set_upload_all_timer(0);
            send_gprs_mb_cmd(0x16, x);
            break;
        default:
            break;
        }
    }
    // 负载功率过高的情况下,直接关闭充电端口
    else if (workStates[x].charge.isRecheckPower != 0)
    {
        pdisp->type = Idle;
        pdisp->addr = x;
        pdisp->data = x + 1;
        pdisp->point = No_Point;
        pdisp->hide = No_Hide;
        auto_show_digitaltube(pdisp);

        if (workStates[x].charge.payType == ONLINE_SWIPE_TYPE)
        {
            set_before_stop_remain_time(get_charge_times(x));
            set_before_stop_remain_battery(get_charge_remain_battery(x));
            set_charge_port_stop_reason(FAULT_STOP);
            set_upload_all_timer(0);
            send_gprs_mb_cmd(0x16, x);
        }
        else if (workStates[x].charge.payType == REMOTE_TYPE)
        {
            set_remote_charge_state(0x0B);
            set_upload_all_timer(0);
            send_gprs_mb_cmd(0x16, x);
        }

        set_rechoose_state(0);
        global.back_timer = 0;
        global.stop_disp_flag = 0;
        stop_charge_port(x);
    }

    workStates[x].charge.isCharge = 0;
    workStates[x].charge.isReconnect = 0;
    workStates[x].charge.isNoConnect = 0;
    workStates[x].charge.isHighPower = 0;
    workStates[x].charge.isRecheckPower = 0;
    workStates[x].charge.state = IDLE_STA;
    workStates[x].work = NoState;

    voice_data.power = 0;
    voice_data.time = 0;
    voice_data.balance = 0;
    spell_voice(&voice_data);
}

// 充电时间倒计时回调函数
static void charge_timer_callback(struct disp_t *pdisp, uint8_t x)
{
    //	static uint8_t t_cnt[TOTAL_PORTS_NUM] = {0};
    uint16_t temp = 0;

    if (workStates[x].charge.back_cnt != 0)
    {
        workStates[x].charge.back_cnt--;
    }
    else
    {
        workStates[x].charge.check_cnt = 0;
    }

    switch (workStates[x].charge.check_cnt)
    {
    case 1:
        pdisp->type = Parameters;
        pdisp->data = workStates[x].charge.chargePower;
        pdisp->hide = No_Hide;

        if (workStates[x].charge.chargePower >= 1000)
        {
            pdisp->point = Pos_2;
            pdisp->data /= 10;
        }
        else
        {
            pdisp->point = No_Point;
        }

        break;
    case 2:
        if (set.initDisplay != 0)
        {
            pdisp->type = Parameters;
            pdisp->data = workStates[x].charge.chargeTimes;
            pdisp->hide = No_Hide;
        }
        else
        {
            pdisp->type = Parameters;
            pdisp->hide = No_Hide;

            if (workStates[x].charge.remainBattery > 9999)
            {
                pdisp->point = No_Point;
                pdisp->data = workStates[x].charge.remainBattery / 100;
            }
            else
            {
                if (workStates[x].charge.remainBattery > 999)
                {
                    pdisp->point = Pos_1;
                    pdisp->data = workStates[x].charge.remainBattery / 10;
                }
                else
                {
                    pdisp->point = Pos_2;
                    pdisp->data = workStates[x].charge.remainBattery;
                }
            }
            if (set.allowedSelfStop == 0 || set.freecharge != 0)
                pdisp->data = 0;
        }
        break;
    case 3:
        pdisp->type = Parameters;
        pdisp->hide = No_Hide;

        if (workStates[x].charge.payType != NO_TYPE)
        {
            if (workStates[x].charge.payType == COIN_TYPE)
            {
                pdisp->point = No_Point;
                pdisp->data = 0;
            }
            else
            {
                if (workStates[x].charge.remainAmount > 999)
                {
                    pdisp->point = No_Point;
                    pdisp->data = workStates[x].charge.remainAmount / 10;
                }
                else
                {
                    pdisp->point = Pos_1;
                    pdisp->data = workStates[x].charge.remainAmount;
                }
            }
        }
        break;
    default:
        // 出现设备未连接或者超出最大功率，闪烁显示，并且每隔20s重新检测一次
        if (workStates[x].charge.isNoConnect != 0 || workStates[x].charge.isHighPower != 0)
        {
            if (global.flip != 0)
                pdisp->hide = Hidden_All;
            else
                pdisp->hide = No_Hide;

            pdisp->point = No_Point;
            pdisp->type = Error;

            if (workStates[x].charge.isNoConnect != 0)
                pdisp->data = DISP_ERROR_NUM;
            else if (workStates[x].charge.isHighPower != 0)
                pdisp->data = DISP_HIGH_NUM;
        }
        else
        {
            if (set.initDisplay != 0)
            {
                if (get_start_check_charge_port(x) != 0)
                    pdisp->type = Check_power;
                else
                    pdisp->type = Parameters;

                pdisp->hide = No_Hide;

                if (workStates[x].charge.remainBattery > 9999)
                {
                    pdisp->point = No_Point;
                    pdisp->data = workStates[x].charge.remainBattery / 100;
                }
                else
                {
                    if (workStates[x].charge.remainBattery > 999)
                    {
                        pdisp->point = Pos_1;
                        pdisp->data = workStates[x].charge.remainBattery / 10;
                    }
                    else
                    {
                        pdisp->point = Pos_2;
                        pdisp->data = workStates[x].charge.remainBattery;
                    }
                }
                if (set.allowedSelfStop == 0 || set.freecharge != 0)
                    pdisp->data = 0;
            }
            else
            {
                if (global.flip == 0)
                {
                    pdisp->point = Pos_0;
                    pdisp->hide = No_Hide;
                }
                else
                {
                    pdisp->point = No_Point;
                    if (get_floatcharge_state(x) != 0)
                    {
                        pdisp->hide = Hidden_All;
                    }
                    else
                    {
                        pdisp->hide = No_Hide;
                    }
                }

                if (get_start_check_charge_port(x) != 0)
                {
                    pdisp->type = Check_power;
                }
                else
                {
                    pdisp->type = Parameters;
                }
                if (get_floatcharge_state(x) != 0)
                {
                    if (get_last_charge_time(x) > get_float_charge_time())
                    {
                        pdisp->data = get_last_charge_time(x) - (get_float_charge_time() - get_charge_times(x));
                    }
                    else
                    {
                        pdisp->data = get_charge_times(x);
                    }
                }
                else
                {
                    pdisp->data = workStates[x].charge.chargeTimes;
                }
            }
        }
        break;
    }

    if (global.stop_disp_flag == 0 && calibrate.isCalibrate == 0 && calibrate.isRefPowerAdjust == 0 && get_end_check_charge_port(x) == 0)
    {
        pdisp->addr = x;
        //		if(workStates[x].charge.check_cnt == 1)
        //		{
        //			if(++t_cnt[x] >= 3)
        //			{
        //				t_cnt[x] = 0;
        //				auto_show_digitaltube(pdisp);
        //			}
        //		}
        //		else
        //		{
        auto_show_digitaltube(pdisp);
        //		}
    }

    if (workStates[x].charge.isNoConnect != 0 || workStates[x].charge.isHighPower != 0)
    {
        workStates[x].charge.error_cnt++;

        // set_detect_overtime(x, 0);

        if (workStates[x].charge.error_cnt == set.start_check_time || workStates[x].charge.error_cnt == (set.start_check_time * 2))
        {
            if (workStates[x].charge.isNoConnect != 0)
            {
                workStates[x].charge.isNoConnect = 0;
            }
            else if (workStates[x].charge.isHighPower != 0)
            {
                workStates[x].charge.isHighPower = 0;

                relay_write(x, GPIO_PIN_SET);
            }
        }
        else if (workStates[x].charge.error_cnt == (set.start_check_time * 3))
        {
            workStates[x].charge.error_cnt = 0;
            workStates[x].charge.minites = 0;
            workStates[x].charge.check_cnt = 0;

            relay_write(x, GPIO_PIN_RESET);

            init_charge_rom(x, D_NORMAL_MODE);
            reset_filter_data(x);

            fault_back_callback(pdisp, x);
        }
    }
    else
    {
        if (++workStates[x].charge.minites >= 60)
        {
            workStates[x].charge.minites = 0;
            workStates[x].charge.chargeTimes--;

            // 充满自停关或计费方式为时间计费,则按照时间百分比计算剩余金额
            if (set.allowedSelfStop == 0 || set.billing_method == 1)
            {
                uint32_t temp_value = 0;
                // 计算剩余可用余额
                temp_value = workStates[x].charge.chargeTimes * 100;
                if (set.allowedSelfStop != 0)
                {
                    if (get_floatcharge_state(x) != 0)
                    {
                        if (get_last_charge_time(x) > get_float_charge_time())
                        {
                            temp_value = get_last_charge_time(x) - (get_float_charge_time() - get_charge_times(x));
                        }
                        else
                        {
                            temp_value = get_charge_times(x);
                        }
                        temp_value *= 100;
                    }
                    temp_value /= get_dynamic_total_charge_time(x);
                }
                else
                {
                    temp_value /= workStates[x].charge.totalChargeTime;
                }

                workStates[x].charge.remainAmount = (workStates[x].charge.totalAmount * temp_value) / 100;
                save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 100 + (x * 2), workStates[x].charge.remainAmount);
            }

            if (workStates[x].charge.chargeTimes != 0)
            {
                pdisp->type = Parameters;
                pdisp->addr = x;
                pdisp->data = workStates[x].charge.chargeTimes;
                pdisp->point = No_Point;
                pdisp->hide = No_Hide;

                if (global.stop_disp_flag == 0 && workStates[x].charge.check_cnt == 0)
                {
                    if (get_floatcharge_state(x) == 0)
                    {
                        auto_show_digitaltube(pdisp);
                    }
                }
                save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 60 + (x * 2), workStates[x].charge.chargeTimes);
            }
            else
            {
                pdisp->type = Idle;
                pdisp->addr = x;
                pdisp->data = x + 1;
                pdisp->point = No_Point;
                pdisp->hide = No_Hide;

                if (global.stop_disp_flag == 0)
                    auto_show_digitaltube(pdisp);

                before_stop_remain_battery = workStates[x].charge.remainBattery;
                if (get_floatcharge_state(x) != 0)
                {
                    stop_reason = AUTO_STOP;
                    before_stop_remain_time = get_last_charge_time(x) - get_float_charge_time();
                }
                else
                {
                    stop_reason = TIME_DONE_STOP;
                    before_stop_remain_time = workStates[x].charge.chargeTimes;
                }

                if (workStates[x].charge.isFloatCharge != 0 && get_charge_paytype(x) == LOCAL_SWIPE_TYPE && set.allowedRecovery != 0)
                {
                    workStates[x].charge.manual_recovery_amount_flag = 1;
                    workStates[x].charge.isCharge = 0;
                    workStates[x].charge.state = IDLE_STA;
                    workStates[x].work = NoState;
                    relay_write(x, GPIO_PIN_RESET);
                    init_charge_rom(x, D_NORMAL_MODE);
                    reset_filter_data(x);
                }
                else
                {
                    stop_charge_port(x);
                }

                if (set.freecharge == 0)
                {
                    set_upload_all_timer(0);
                    send_gprs_mb_cmd(0x16, x);
                }
            }
        }
        if (get_start_check_charge_port(x) != 0)
            workStates[x].charge.minites = 0;
    }
}

// 后台计时管理回调函数
static void background_timer_callback(struct disp_t *pdisp, uint8_t x)
{
    uint16_t temp = 0;

    if (test_tab[x].isTest != 0)
    {
        if (--test.time == 0)
        {
            pdisp->type = Error;
            pdisp->data = DISP_LINE_NUM;
            workStates[x].work = NoState;
            workStates[x].charge.state = IDLE_STA;

            test_tab[x].isTest = 0;
            test.last_port = 0;

            init_charge_rom(x, D_NORMAL_MODE);
            reset_filter_data(x);

            relay_write(x, GPIO_PIN_RESET);
        }
        else
        {
            pdisp->type = Parameters;
            pdisp->data = test.time;
        }

        pdisp->addr = x;
        pdisp->point = No_Point;
        pdisp->hide = No_Hide;
        auto_show_digitaltube(pdisp);
    }
    else if (workStates[x].charge.isCharge != 0)
    {
        if (++workStates[x].charge.minites >= 60)
        {
            workStates[x].charge.minites = 0;
            workStates[x].charge.chargeTimes--;

            // 充满自停关或计费方式为时间计费,则按照时间百分比计算剩余金额
            if (set.allowedSelfStop == 0 || set.billing_method == 1)
            {
                uint32_t temp_value = 0;
                // 计算剩余可用余额
                temp_value = workStates[x].charge.chargeTimes * 100;
                if (set.allowedSelfStop != 0)
                {
                    if (get_floatcharge_state(x) != 0)
                    {
                        if (get_last_charge_time(x) > get_float_charge_time())
                        {
                            temp_value = get_last_charge_time(x) - (get_float_charge_time() - get_charge_times(x));
                        }
                        else
                        {
                            temp_value = get_charge_times(x);
                        }
                        temp_value *= 100;
                    }
                    temp_value /= get_dynamic_total_charge_time(x);
                }
                else
                {
                    temp_value /= workStates[x].charge.totalChargeTime;
                }

                workStates[x].charge.remainAmount = (workStates[x].charge.totalAmount * temp_value) / 100;
                save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 100 + (x * 2), workStates[x].charge.remainAmount);
            }

            if (workStates[x].charge.chargeTimes != 0)
            {
                pdisp->type = Parameters;
                pdisp->addr = x;
                pdisp->data = workStates[x].charge.chargeTimes;
                pdisp->point = No_Point;
                pdisp->hide = No_Hide;
                if (global.stop_disp_flag == 0)
                    auto_show_digitaltube(pdisp);
                save_parameters(POWER_OUTAGE_PARA_16BIT_ADDR + 60 + (x * 2), workStates[x].charge.chargeTimes);
            }
            else
            {
                pdisp->type = Idle;
                pdisp->addr = x;
                pdisp->data = x + 1;
                pdisp->point = No_Point;
                pdisp->hide = No_Hide;
                auto_show_digitaltube(pdisp);

                before_stop_remain_battery = workStates[x].charge.remainBattery;
                if (get_floatcharge_state(x) != 0)
                {
                    stop_reason = AUTO_STOP;
                    before_stop_remain_time = get_last_charge_time(x) - get_float_charge_time();
                }
                else
                {
                    stop_reason = TIME_DONE_STOP;
                    before_stop_remain_time = workStates[x].charge.chargeTimes;
                }

                if (workStates[x].charge.isFloatCharge != 0 && get_charge_paytype(x) == LOCAL_SWIPE_TYPE && set.allowedRecovery != 0)
                {
                    workStates[x].charge.manual_recovery_amount_flag = 1;
                    workStates[x].charge.isCharge = 0;
                    workStates[x].charge.state = IDLE_STA;
                    workStates[x].work = NoState;
                    relay_write(x, GPIO_PIN_RESET);
                    init_charge_rom(x, D_NORMAL_MODE);
                    reset_filter_data(x);
                }
                else
                {
                    stop_charge_port(x);
                }

                if (set.freecharge == 0)
                {
                    set_upload_all_timer(0);
                    send_gprs_mb_cmd(0x16, x);
                }
            }
        }
        if (get_start_check_charge_port(x) != 0)
            workStates[x].charge.minites = 0;
    }
}

// 功能服务函数
static void service_Fun(struct disp_t *pdisp, uint8_t cmd)
{
    switch (cmd)
    {
    case 0x0B:
        set_menu(pdisp);
        break;
    case 0x0C:
        set_add_fun(pdisp);
        break;
    case 0x0D:
        set_sub_fun(pdisp);
        break;
    case 0x0E:
        coinFun(pdisp);
        break;
    default:
        if (set.isSet != 0 || calibrate.isRefPowerAdjust != 0 || clear.isClear != 0 || calibrate.isCalibrate != 0)
        {
            if ((cmd == 1 || cmd == 2) && calibrate.isRefPowerAdjust != 0)
            {
                if (cmd == 1)
                    calibrate_add_fun(pdisp);
                else if (cmd == 2)
                    calibrate_sub_fun(pdisp);
            }
            else if (calibrate.isCalibrate != 0)
            {
                calibrate_fun(cmd);
            }
            else if (clear.isClear != 0)
            {
                clear_fun(pdisp, cmd);
            }
        }
        else
        {
            test_fun(pdisp, cmd);
            charge_fun(pdisp, cmd);
        }
        break;
    }
}

// 定时器处理用的动态线程函数
static void timer_dynamic_thread_entry(void *arg)
{
    t_voice_data voice_data;
    uint8_t i = 0;
    static uint8_t tt_count = 0;

    struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);

    tm1640_init();

    tt_count++;
    if (tt_count >= 2)
    {
        tt_count = 0;

        global.flip = ~global.flip;

        online_card_over_time(pdisp);

        if (clear.isClear != 0 || global.stop_disp_flag != 0 || get_card_exit_recovery_balance_state() != 0) // && get_card_recovery_balance_state() == 0)
        {
            if (global.back_timer != 0)
                global.back_timer--;
            if (global.back_timer == 0)
            {
                if (set.freecharge == 0)
                {
                    COIN_ENABLE;
                    set_local_card_state(0);
                    set_online_card_state(0);
                }
                else
                {
                    COIN_DISABLE;
                    set_local_card_state(1);
                    set_online_card_state(1);
                }

                clear.isClear = 0;
                if (set.isSet != 0)
                {
                    //                speeker(Voice_Commd_Exit_Setmode);
                    voice_data.type = Voice_Commd_Exit_Setmode;
                    voice_data.power = 0;
                    voice_data.time = 0;
                    voice_data.balance = 0;
                    spell_voice(&voice_data);
                }

                set.isSet = 0;
                set_card_exti_recovery_state(0);

                global.stop_disp_flag = 0;
                global.isErrorSta = 0;

                swipe_noreponse_over_time();

                for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                {
                    main_view(pdisp, i);
                }
            }
        }

        for (i = 0; i < TOTAL_PORTS_NUM; i++)
        {
            switch (workStates[i].work)
            {
            case NomalChargeState:
                charge_timer_callback(pdisp, i);
                break;
            case ClearState:
                background_timer_callback(pdisp, i);
                break;
            case TestState:
                test_timer_callback(pdisp, i);
                break;
            case CalibrateState:
                calibrate_timer_callback(pdisp, i);
                break;
            default:
                break;
            }
        }
    }

    if (set.isSet == 0 && clear.isClear == 0 && global.stop_disp_flag == 0 && calibrate.isCalibrate == 0 && calibrate.isRefPowerAdjust == 0 && calibrate.isCalibrating == 0 && get_card_exit_recovery_balance_state() == 0 && get_card_recovery_balance_state() == 0)
    {
        for (i = 0; i < TOTAL_PORTS_NUM; i++)
        {
            if (workStates[i].work == NoState)
            {
                main_view(pdisp, i);
            }
        }
    }

    rt_free_align(pdisp);
}

// 创建定时器处理动态线程控制块
#define TIMER_THREAD_STACK_SIZE 600
static rt_thread_t dynamic_timer = RT_NULL;
void create_timer_dynamic_thread(void)
{
    /* 创建线程 1，名称是 thread1，入口是 thread1_entry*/
    dynamic_timer = rt_thread_create("d_timer",
                                     timer_dynamic_thread_entry,
                                     RT_NULL,
                                     TIMER_THREAD_STACK_SIZE,
                                     2,
                                     5);

    /* 如果获得线程控制块，启动这个线程 */
    if (dynamic_timer != RT_NULL)
    {
        //        rt_kprintf("create transmit dynamic thread sucess \r\n");
        rt_thread_startup(dynamic_timer);
    }
}

static uint8_t key_num = 0;

// 按键处理用的动态线程函数
static void process_dynamic_thread_entry(void *arg)
{
    struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);

    service_Fun(pdisp, key_num);

    rt_free_align(pdisp);
}

// 创建按键处理动态线程控制块
#define PROCESS_THREAD_STACK_SIZE 600
static rt_thread_t dynamic_process = RT_NULL;
void create_process_dynamic_thread(void)
{
    /* 创建线程 1，名称是 thread1，入口是 thread1_entry*/
    dynamic_process = rt_thread_create("d_process",
                                       process_dynamic_thread_entry,
                                       RT_NULL,
                                       PROCESS_THREAD_STACK_SIZE,
                                       2,
                                       5);

    /* 如果获得线程控制块，启动这个线程 */
    if (dynamic_process != RT_NULL)
    {
        //        rt_kprintf("create transmit dynamic thread sucess \r\n");
        rt_thread_startup(dynamic_process);
    }
}

// 按键功能线程入口函数
static void process_entry(void *arg)
{
    uint8_t cmd = 0;

    while (1)
    {
        /* 从邮箱中收取邮件 */
        if (rt_mb_recv(&key_mb, (rt_ubase_t *)&cmd, RT_WAITING_FOREVER) == RT_EOK)
        {
            key_num = cmd;
            if (get_card_exit_recovery_balance_state() != 0)
            {
                set_card_exti_recovery_state(0);

                struct disp_t *pdisp = rt_malloc_align(sizeof(struct disp_t), 4);

                global.stop_disp_flag = 0;
                global.back_timer = 0;
                for (uint8_t i = 0; i < TOTAL_PORTS_NUM; i++)
                {
                    main_view(pdisp, i);
                }

                rt_free_align(pdisp);
            }
            else
            {
                // 创建动态处理线程,处理完成自动删除,回收内存
                create_process_dynamic_thread();
            }
        }
    }
}

struct rt_semaphore detect_sem;
/* 邮箱控制块 */
struct rt_mailbox gprs_transmit_mb;
// 定时器线程入口函数
static void timer_entry(void *arg)
{
    static uint8_t t_cnt = 0, dt_cnt = 0, ut_cnt = 0;

    // 喂狗,程序跑死跑飞自动重启
#ifdef WATCH_DOG
    IWDG_Feed();
#endif

    t_cnt++;
    dt_cnt++;
    ut_cnt++;
    if (t_cnt >= 5)
    {
        t_cnt = 0;
        // 创建动态处理线程,处理完成自动删除,回收内存
        create_timer_dynamic_thread();
    }

    if (ut_cnt >= 10)
    {
        ut_cnt = 0;

        // 每间隔5分钟,上传一次所有端口的信息
        global.upload_timer_cnt++;
        if (global.upload_timer_cnt >= 300 && set.freecharge == 0)
        {
            global.upload_timer_cnt = 0;
            send_gprs_mb_cmd(0x21, 0);
        }
    }

    if (dt_cnt >= 2)
    {
        dt_cnt = 0;
        // 向功率检测线程发送信号
        rt_sem_release(&detect_sem);
    }
}

#define PROCESS_THREAD_PRIORITY 5
#define PROCESS_THREAD_TIMESLICE 1

ALIGN(RT_ALIGN_SIZE)
static char process_stack[350];
static struct rt_thread process;
/* 定时器的控制块 */
static struct rt_timer timer;
// 创建线程
int process_thread(void)
{
    rt_err_t result;

    /* 初始化信号量 */
    rt_sem_init(&detect_sem,
                "detect_sem",
                0,
                RT_IPC_FLAG_FIFO);

    rt_thread_init(&process,
                   "process",
                   process_entry,
                   RT_NULL,
                   &process_stack[0],
                   sizeof(process_stack),
                   PROCESS_THREAD_PRIORITY,
                   1);
    rt_thread_startup(&process);

    /* 初始化定时器 */
    rt_timer_init(&timer,
                  "timer",                                            /* 定时器名字是 timer1 */
                  timer_entry,                                        /* 超时时回调的处理函数 */
                  RT_NULL,                                            /* 超时函数的入口参数 */
                  100,                                                /* 定时长度，以 OS Tick 为单位，即 10 个 OS Tick */
                  RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER); /* 周期性定时器 */
    /* 启动定时器 */
    rt_timer_start(&timer);

#ifdef WATCH_DOG
    IWDG_Init();
#endif

    return RT_EOK;
}
INIT_APP_EXPORT(process_thread);

/* 消息队列中用到的放置消息的内存池 */
static uint8_t mb_pool[4];
int init_msgqueue(void)
{
    rt_err_t result;

    /* 初始化消息队列 */
    result = rt_mb_init(&gprs_transmit_mb,
                        "gprs_transmit_mb",
                        &mb_pool[0],         /* 邮箱用到的内存池是 mb_pool */
                        sizeof(mb_pool) / 4, /* 邮箱中的邮件数目，因为一封邮件占 4 字节 */
                        RT_IPC_FLAG_FIFO);   /* 采用 FIFO 方式进行线程等待 */

    if (result != RT_EOK)
    {
        //        rt_kprintf("init gprs_transmit_mq message queue failed.\n");
        return RT_ERROR;
    }

    return RT_EOK;
}
INIT_APP_EXPORT(init_msgqueue);
