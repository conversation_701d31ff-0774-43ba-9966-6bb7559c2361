#ifndef __APP_POWERDETECT_H__

#include "stdbool.h"

#include "stm32f0xx.h"
#include "rtthread.h"

#include "drv_tm1640.h"
#include "drv_hlw8012.h"

#define MIN_POWER_VALUE             10
#define MAX_STICK_POWER_VALUE 		50
//#define BILLING_POWER_REF_VALUE    100

#define ABS(x) ((x)>=0?(x):-(x))

#define SMOKE_ALARM     (GPIOB->IDR & GPIO_PIN_7)

extern uint16_t get_dynamic_total_charge_time(uint8_t x);
extern uint8_t get_power_safe_flag(void);
extern uint32_t get_relay_error_state(void);
extern uint32_t get_total_charge_power(void);
extern uint8_t get_charge_time_level(uint8_t x);
extern uint8_t get_smoke_alarm_state(void);
extern uint8_t get_fire_alarm_state(void);
extern uint8_t get_overpower_alarm_state(void);
extern uint8_t get_overtemp_alarm_state(void);
extern uint8_t get_temp_value(void);
extern uint8_t get_temp_symbol(void);
extern uint8_t get_billing_type(uint8_t x);
extern uint8_t get_start_check_charge_port(uint8_t x);
extern uint8_t get_end_check_charge_port(uint8_t x);

extern void set_detect_power_cnt(uint8_t x, uint8_t temp);
extern void set_stop_recal_flag(uint8_t x, uint8_t temp);
extern void set_dynamic_total_charge_time(uint8_t x, uint16_t temp);
extern void set_power_safe_flag(uint8_t temp);
extern void set_charge_time_level(uint8_t x, uint8_t temp);
extern void set_end_check_charge_port(uint8_t x, uint8_t sta);
extern void set_check_charge_port_cnt(uint8_t x, uint8_t temp);
extern void set_start_check_charge_port(uint8_t x, uint8_t sta);
extern void set_detect_state(uint8_t x, uint8_t sta);
extern void set_detect_overtime(uint8_t x, uint8_t temp);
extern void set_remote_sucess_flag(uint8_t x, uint8_t sta);
extern void set_detect_reserve_max_power(uint8_t x, uint16_t temp);
extern void set_detect_last_max_power(uint8_t x, uint16_t temp);

#endif

